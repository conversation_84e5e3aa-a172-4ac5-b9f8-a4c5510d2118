(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){A(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function A(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return U(G)=="symbol"?G:String(G)}function z(C,G){if(U(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,GC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},S={lessThanXSeconds:{one:"mindre enn ett sekund",other:"mindre enn {{count}} sekunder"},xSeconds:{one:"ett sekund",other:"{{count}} sekunder"},halfAMinute:"et halvt minutt",lessThanXMinutes:{one:"mindre enn ett minutt",other:"mindre enn {{count}} minutter"},xMinutes:{one:"ett minutt",other:"{{count}} minutter"},aboutXHours:{one:"omtrent en time",other:"omtrent {{count}} timer"},xHours:{one:"en time",other:"{{count}} timer"},xDays:{one:"en dag",other:"{{count}} dager"},aboutXWeeks:{one:"omtrent en uke",other:"omtrent {{count}} uker"},xWeeks:{one:"en uke",other:"{{count}} uker"},aboutXMonths:{one:"omtrent en m\xE5ned",other:"omtrent {{count}} m\xE5neder"},xMonths:{one:"en m\xE5ned",other:"{{count}} m\xE5neder"},aboutXYears:{one:"omtrent ett \xE5r",other:"omtrent {{count}} \xE5r"},xYears:{one:"ett \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"over ett \xE5r",other:"over {{count}} \xE5r"},almostXYears:{one:"nesten ett \xE5r",other:"nesten {{count}} \xE5r"}},D=function C(G,H,J){var X,Y=S[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"om "+X;else return X+" siden";return X};function E(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},R={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},V={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:E({formats:M,defaultWidth:"full"}),time:E({formats:R,defaultWidth:"full"}),dateTime:E({formats:V,defaultWidth:"full"})},j={lastWeek:"'forrige' eeee 'kl.' p",yesterday:"'i g\xE5r kl.' p",today:"'i dag kl.' p",tomorrow:"'i morgen kl.' p",nextWeek:"EEEE 'kl.' p",other:"P"},w=function C(G,H,J,X){return j[G]};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var B=C.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[q]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var f={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["f\xF8r Kristus","etter Kristus"]},_={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},P={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mars","apr.","mai","juni","juli","aug.","sep.","okt.","nov.","des."],wide:["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember"]},v={narrow:["S","M","T","O","T","F","L"],short:["s\xF8","ma","ti","on","to","fr","l\xF8"],abbreviated:["s\xF8n","man","tir","ons","tor","fre","l\xF8r"],wide:["s\xF8ndag","mandag","tirsdag","onsdag","torsdag","fredag","l\xF8rdag"]},F={narrow:{am:"a",pm:"p",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natten"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natten"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morgenen",afternoon:"p\xE5 ettermiddagen",evening:"p\xE5 kvelden",night:"p\xE5 natten"}},k=function C(G,H){var J=Number(G);return J+"."},b={ordinalNumber:k,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:_,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:P,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:F,defaultWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(B)?m(B,function(K){return K.test(Z)}):h(B,function(K){return K.test(Z)}),T;T=C.valueCallback?C.valueCallback(q):q,T=H.valueCallback?H.valueCallback(T):T;var CC=G.slice(Z.length);return{value:T,rest:CC}}}function h(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function m(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function y(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var B=G.slice(X.length);return{value:Z,rest:B}}}var c=/^(\d+)\.?/i,d=/\d+/i,g={narrow:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,abbreviated:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,wide:/^(før Kristus|før vår tid|etter Kristus|vår tid)/i},p={any:[/^f/i,/^e/i]},u={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? kvartal/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mars?|apr|mai|juni?|juli?|aug|sep|okt|nov|des)\.?/i,wide:/^(januar|februar|mars|april|mai|juni|juli|august|september|oktober|november|desember)/i},n={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^aug/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[smtofl]/i,short:/^(sø|ma|ti|on|to|fr|lø)/i,abbreviated:/^(søn|man|tir|ons|tor|fre|lør)/i,wide:/^(søndag|mandag|tirsdag|onsdag|torsdag|fredag|lørdag)/i},o={any:[/^s/i,/^m/i,/^ti/i,/^o/i,/^to/i,/^f/i,/^l/i]},r={narrow:/^(midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten)|[ap])/i,any:/^([ap]\.?\s?m\.?|midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten))/i},a={any:{am:/^a(\.?\s?m\.?)?$/i,pm:/^p(\.?\s?m\.?)?$/i,midnight:/^midn/i,noon:/^midd/i,morning:/morgen/i,afternoon:/ettermiddag/i,evening:/kveld/i,night:/natt/i}},e={ordinalNumber:y({matchPattern:c,parsePattern:d,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"nb",formatDistance:D,formatLong:L,formatRelative:w,localize:b,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{nb:t})})})();

//# debugId=1B2495C4973C6E4464756E2164756E21
