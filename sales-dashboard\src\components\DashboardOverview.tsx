'use client';

import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

// Sample data
const salesData = [
  { month: 'Jan', sales: 4000, leads: 240 },
  { month: 'Feb', sales: 3000, leads: 139 },
  { month: 'Mar', sales: 2000, leads: 980 },
  { month: 'Apr', sales: 2780, leads: 390 },
  { month: 'May', sales: 1890, leads: 480 },
  { month: 'Jun', sales: 2390, leads: 380 },
];

const leadSourceData = [
  { name: 'Website', value: 400, color: '#0088FE' },
  { name: 'Social Media', value: 300, color: '#00C49F' },
  { name: '<PERSON><PERSON>', value: 300, color: '#FFBB28' },
  { name: 'Referral', value: 200, color: '#FF8042' },
];

const communicationData = [
  { type: 'Email', count: 45 },
  { type: 'Phone', count: 23 },
  { type: 'Text', count: 12 },
  { type: 'Meeting', count: 8 },
];

const stats = [
  { name: 'Total Revenue', value: '$45,231', change: '+20.1%', changeType: 'positive', icon: CurrencyDollarIcon },
  { name: 'Active Leads', value: '1,234', change: '+15.3%', changeType: 'positive', icon: ArrowUpIcon },
  { name: 'Calls Made', value: '89', change: '-2.4%', changeType: 'negative', icon: PhoneIcon },
  { name: 'Emails Sent', value: '156', change: '****%', changeType: 'positive', icon: EnvelopeIcon },
];

export default function DashboardOverview() {
  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Dashboard Overview</h1>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
            Add Lead
          </button>
          <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
            New Contact
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stat.value}</div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.changeType === 'positive' ? (
                        <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                      ) : (
                        <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                      )}
                      <span className="sr-only">{stat.changeType === 'positive' ? 'Increased' : 'Decreased'} by</span>
                      {stat.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Sales Trend Chart */}
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Sales & Leads Trend</h3>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={salesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="sales" stroke="#3B82F6" strokeWidth={2} />
              <Line type="monotone" dataKey="leads" stroke="#10B981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Lead Sources Pie Chart */}
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Lead Sources</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={leadSourceData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {leadSourceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Communication Activity */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Communication Activity (This Week)</h3>
        <ResponsiveContainer width="100%" height={250}>
          <BarChart data={communicationData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="type" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="count" fill="#3B82F6" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {[
            { action: 'New lead added', contact: 'John Smith', time: '2 hours ago', type: 'lead' },
            { action: 'Email sent', contact: 'Sarah Johnson', time: '4 hours ago', type: 'email' },
            { action: 'Call completed', contact: 'Mike Davis', time: '6 hours ago', type: 'call' },
            { action: 'Meeting scheduled', contact: 'Lisa Wilson', time: '1 day ago', type: 'meeting' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg">
              <div className={`w-2 h-2 rounded-full ${
                activity.type === 'lead' ? 'bg-green-500' :
                activity.type === 'email' ? 'bg-blue-500' :
                activity.type === 'call' ? 'bg-yellow-500' : 'bg-purple-500'
              }`} />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                <p className="text-sm text-gray-500">{activity.contact}</p>
              </div>
              <span className="text-xs text-gray-400">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
