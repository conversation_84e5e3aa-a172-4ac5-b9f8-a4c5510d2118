(()=>{var A;function C(B){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},C(B)}function x(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),H.push.apply(H,J)}return H}function Q(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){N(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function N(B,G,H){if(G=z(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function z(B){var G=E(B,"string");return C(G)=="symbol"?G:String(G)}function E(B,G){if(C(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(C(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,HB=function B(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},S={lessThanXSeconds:{one:"inqas minn sekonda",other:"inqas minn {{count}} sekondi"},xSeconds:{one:"sekonda",other:"{{count}} sekondi"},halfAMinute:"nofs minuta",lessThanXMinutes:{one:"inqas minn minuta",other:"inqas minn {{count}} minuti"},xMinutes:{one:"minuta",other:"{{count}} minuti"},aboutXHours:{one:"madwar sieg\u0127a",other:"madwar {{count}} sieg\u0127at"},xHours:{one:"sieg\u0127a",other:"{{count}} sieg\u0127at"},xDays:{one:"\u0121urnata",other:"{{count}} \u0121ranet"},aboutXWeeks:{one:"madwar \u0121img\u0127a",other:"madwar {{count}} \u0121img\u0127at"},xWeeks:{one:"\u0121img\u0127a",other:"{{count}} \u0121img\u0127at"},aboutXMonths:{one:"madwar xahar",other:"madwar {{count}} xhur"},xMonths:{one:"xahar",other:"{{count}} xhur"},aboutXYears:{one:"madwar sena",two:"madwar sentejn",other:"madwar {{count}} snin"},xYears:{one:"sena",two:"sentejn",other:"{{count}} snin"},overXYears:{one:"aktar minn sena",two:"aktar minn sentejn",other:"aktar minn {{count}} snin"},almostXYears:{one:"kwa\u017Ci sena",two:"kwa\u017Ci sentejn",other:"kwa\u017Ci {{count}} snin"}},M=function B(G,H,J){var X,Y=S[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else if(H===2&&Y.two)X=Y.two;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"f'"+X;else return X+" ilu";return X};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var D={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:D,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'li g\u0127adda' 'fil-'p",yesterday:"'Il-biera\u0127 fil-'p",today:"'Illum fil-'p",tomorrow:"'G\u0127ada fil-'p",nextWeek:"eeee 'fil-'p",other:"P"},w=function B(G,H,J,X){return j[G]};function I(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var T=B.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;X=B.values[q]||B.values[T]}var U=B.argumentCallback?B.argumentCallback(G):G;return X[U]}}var _={narrow:["Q","W"],abbreviated:["QK","WK"],wide:["qabel Kristu","wara Kristu"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1. kwart","2. kwart","3. kwart","4. kwart"]},v={narrow:["J","F","M","A","M","\u0120","L","A","S","O","N","D"],abbreviated:["Jan","Fra","Mar","Apr","Mej","\u0120un","Lul","Aww","Set","Ott","Nov","Di\u010B"],wide:["Jannar","Frar","Marzu","April","Mejju","\u0120unju","Lulju","Awwissu","Settembru","Ottubru","Novembru","Di\u010Bembru"]},F={narrow:["\u0126","T","T","E","\u0126","\u0120","S"],short:["\u0126a","Tn","Tl","Er","\u0126a","\u0120i","Si"],abbreviated:["\u0126ad","Tne","Tli","Erb","\u0126am","\u0120im","Sib"],wide:["Il-\u0126add","It-Tnejn","It-Tlieta","L-Erbg\u0127a","Il-\u0126amis","Il-\u0120img\u0127a","Is-Sibt"]},P={narrow:{am:"a",pm:"p",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"},abbreviated:{am:"AM",pm:"PM",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"},wide:{am:"a.m.",pm:"p.m.",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"}},k={narrow:{am:"a",pm:"p",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"},abbreviated:{am:"AM",pm:"PM",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"},wide:{am:"a.m.",pm:"p.m.",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"}},b=function B(G,H){var J=Number(G);return J+"\xBA"},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],T=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(T)?y(T,function(K){return K.test(Z)}):m(T,function(K){return K.test(Z)}),U;U=B.valueCallback?B.valueCallback(q):q,U=H.valueCallback?H.valueCallback(U):U;var GB=G.slice(Z.length);return{value:U,rest:GB}}}function m(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function y(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function c(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var X=J[0],Y=G.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var T=G.slice(X.length);return{value:Z,rest:T}}}var p=/^(\d+)(º)?/i,d=/\d+/i,g={narrow:/^(q|w)/i,abbreviated:/^(q\.?\s?k\.?|b\.?\s?c\.?\s?e\.?|w\.?\s?k\.?)/i,wide:/^(qabel kristu|before common era|wara kristu|common era)/i},u={any:[/^(q|b)/i,/^(w|c)/i]},l={narrow:/^[1234]/i,abbreviated:/^k[1234]/i,wide:/^[1234](\.)? kwart/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmaglsond]/i,abbreviated:/^(jan|fra|mar|apr|mej|ġun|lul|aww|set|ott|nov|diċ)/i,wide:/^(jannar|frar|marzu|april|mejju|ġunju|lulju|awwissu|settembru|ottubru|novembru|diċembru)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^ġ/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mej/i,/^ġ/i,/^l/i,/^aw/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[ħteġs]/i,short:/^(ħa|tn|tl|er|ħa|ġi|si)/i,abbreviated:/^(ħad|tne|tli|erb|ħam|ġim|sib)/i,wide:/^(il-ħadd|it-tnejn|it-tlieta|l-erbgħa|il-ħamis|il-ġimgħa|is-sibt)/i},r={narrow:[/^ħ/i,/^t/i,/^t/i,/^e/i,/^ħ/i,/^ġ/i,/^s/i],any:[/^(il-)?ħad/i,/^(it-)?tn/i,/^(it-)?tl/i,/^(l-)?er/i,/^(il-)?ham/i,/^(il-)?ġi/i,/^(is-)?si/i]},a={narrow:/^(a|p|f'nofsillejl|f'nofsinhar|(ta') (għodwa|wara nofsinhar|filgħaxija|lejl))/i,any:/^([ap]\.?\s?m\.?|f'nofsillejl|f'nofsinhar|(ta') (għodwa|wara nofsinhar|filgħaxija|lejl))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^f'nofsillejl/i,noon:/^f'nofsinhar/i,morning:/għodwa/i,afternoon:/wara(\s.*)nofsinhar/i,evening:/filgħaxija/i,night:/lejl/i}},t={ordinalNumber:c({matchPattern:p,parsePattern:d,valueCallback:function B(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"mt",formatDistance:M,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{mt:BB})})})();

//# debugId=2F0FB754D573B01C64756E2164756E21
