'use client';

import { useState } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import AddLeadModal from './AddLeadModal';
import CommunicationModal from './CommunicationModal';

interface Lead {
  id: number;
  name: string;
  email: string;
  phone: string;
  company: string;
  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'closed';
  score: number;
  source: string;
  value: number;
  lastContact: string;
}

const sampleLeads: Lead[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Tech Corp',
    status: 'new',
    score: 85,
    source: 'Website',
    value: 15000,
    lastContact: '2024-01-15'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Innovation Inc',
    status: 'contacted',
    score: 72,
    source: 'Social Media',
    value: 25000,
    lastContact: '2024-01-14'
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Business Solutions',
    status: 'qualified',
    score: 91,
    source: 'Referral',
    value: 35000,
    lastContact: '2024-01-13'
  },
];

const statusColors = {
  new: 'bg-blue-100 text-blue-800',
  contacted: 'bg-yellow-100 text-yellow-800',
  qualified: 'bg-green-100 text-green-800',
  proposal: 'bg-purple-100 text-purple-800',
  closed: 'bg-gray-100 text-gray-800',
};

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>(sampleLeads);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showCommunicationModal, setShowCommunicationModal] = useState(false);
  const [communicationType, setCommunicationType] = useState<'email' | 'call' | 'text' | 'meeting'>('email');
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleAddLead = (leadData: any) => {
    setLeads([...leads, leadData]);
    alert(`Lead "${leadData.name}" has been added successfully!`);
  };

  const handleCommunication = (lead: Lead, type: 'email' | 'call' | 'text' | 'meeting') => {
    setSelectedLead(lead);
    setCommunicationType(type);
    setShowCommunicationModal(true);
  };

  const handleCommunicationSubmit = (communicationData: any) => {
    console.log('Communication logged:', communicationData);
    alert(`${communicationData.type.charAt(0).toUpperCase() + communicationData.type.slice(1)} ${communicationData.status} successfully!`);
  };

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Leads Management</h1>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Add Lead
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search leads..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="qualified">Qualified</option>
            <option value="proposal">Proposal</option>
            <option value="closed">Closed</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">{leads.length}</div>
          <div className="text-sm text-gray-500">Total Leads</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="text-2xl font-bold text-green-600">
            {leads.filter(l => l.status === 'qualified').length}
          </div>
          <div className="text-sm text-gray-500">Qualified</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="text-2xl font-bold text-yellow-600">
            {leads.filter(l => l.status === 'contacted').length}
          </div>
          <div className="text-sm text-gray-500">In Progress</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="text-2xl font-bold text-purple-600">
            ${leads.reduce((sum, lead) => sum + lead.value, 0).toLocaleString()}
          </div>
          <div className="text-sm text-gray-500">Total Value</div>
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lead
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact Info
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLeads.map((lead) => (
                <tr key={lead.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{lead.name}</div>
                      <div className="text-sm text-gray-500">{lead.company}</div>
                      <div className="text-xs text-gray-400">{lead.source}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{lead.email}</div>
                    <div className="text-sm text-gray-500">{lead.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[lead.status]}`}>
                      {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-medium ${getScoreColor(lead.score)}`}>
                      {lead.score}/100
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${lead.value.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleCommunication(lead, 'call')}
                        className="text-blue-600 hover:text-blue-900"
                        title="Make Call"
                      >
                        <PhoneIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleCommunication(lead, 'email')}
                        className="text-green-600 hover:text-green-900"
                        title="Send Email"
                      >
                        <EnvelopeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleCommunication(lead, 'text')}
                        className="text-purple-600 hover:text-purple-900"
                        title="Send Message"
                      >
                        <ChatBubbleLeftIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden space-y-4">
        {filteredLeads.map((lead) => (
          <div key={lead.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{lead.name}</h3>
                <p className="text-sm text-gray-500">{lead.company}</p>
                <p className="text-xs text-gray-400">{lead.source}</p>
              </div>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[lead.status]}`}>
                {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500">Email</p>
                <p className="text-sm text-gray-900 truncate">{lead.email}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Phone</p>
                <p className="text-sm text-gray-900">{lead.phone}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Score</p>
                <p className={`text-sm font-medium ${getScoreColor(lead.score)}`}>
                  {lead.score}/100
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Value</p>
                <p className="text-sm text-gray-900">${lead.value.toLocaleString()}</p>
              </div>
            </div>

            <div className="flex justify-between items-center pt-3 border-t border-gray-200">
              <span className="text-xs text-gray-500">Last contact: {lead.lastContact}</span>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleCommunication(lead, 'call')}
                  className="text-blue-600 hover:text-blue-900"
                  title="Make Call"
                >
                  <PhoneIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleCommunication(lead, 'email')}
                  className="text-green-600 hover:text-green-900"
                  title="Send Email"
                >
                  <EnvelopeIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleCommunication(lead, 'text')}
                  className="text-purple-600 hover:text-purple-900"
                  title="Send Message"
                >
                  <ChatBubbleLeftIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modals */}
      <AddLeadModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddLead}
      />

      <CommunicationModal
        isOpen={showCommunicationModal}
        onClose={() => setShowCommunicationModal(false)}
        onSubmit={handleCommunicationSubmit}
        type={communicationType}
        contactName={selectedLead?.name || ''}
      />
    </div>
  );
}
