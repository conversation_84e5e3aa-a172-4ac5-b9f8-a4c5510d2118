{"version": 3, "sources": ["lib/locale/kk/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}function _slicedToArray(arr, i) {return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();}function _nonIterableRest() {throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");}function _unsupportedIterableToArray(o, minLen) {if (!o) return;if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);var n = Object.prototype.toString.call(o).slice(8, -1);if (n === \"Object\" && o.constructor) n = o.constructor.name;if (n === \"Map\" || n === \"Set\") return Array.from(o);if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);}function _arrayLikeToArray(arr, len) {if (len == null || len > arr.length) len = arr.length;for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];return arr2;}function _iterableToArrayLimit(r, l) {var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];if (null != t) {var e,n,i,u,a = [],f = !0,o = !1;try {if (i = (t = t.call(r)).next, 0 === l) {if (Object(t) !== t) return;f = !1;} else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);} catch (r) {o = !0, n = r;} finally {try {if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;} finally {if (o) throw n;}}return a;}}function _arrayWithHoles(arr) {if (Array.isArray(arr)) return arr;}function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/kk/_lib/formatDistance.js\nfunction declension(scheme, count) {\n  if (scheme.one && count === 1)\n  return scheme.one;\n  var rem10 = count % 10;\n  var rem100 = count % 100;\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: \"1 \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      singularNominative: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      singularGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      pluralGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u0430\\u0437\"\n    },\n    future: {\n      one: \"\\u0431\\u0456\\u0440 \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularNominative: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  xSeconds: {\n    regular: {\n      singularNominative: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\",\n      singularGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\",\n      pluralGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\"\n    },\n    past: {\n      singularNominative: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434 \\u0431\\u04B1\\u0440\\u044B\\u043D\",\n      singularGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434 \\u0431\\u04B1\\u0440\\u044B\\u043D\",\n      pluralGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434 \\u0431\\u04B1\\u0440\\u044B\\u043D\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  halfAMinute: function halfAMinute(options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"\\u0436\\u0430\\u0440\\u0442\\u044B \\u043C\\u0438\\u043D\\u0443\\u0442 \\u0456\\u0448\\u0456\\u043D\\u0434\\u0435\";\n      } else {\n        return \"\\u0436\\u0430\\u0440\\u0442\\u044B \\u043C\\u0438\\u043D\\u0443\\u0442 \\u0431\\u04B1\\u0440\\u044B\\u043D\";\n      }\n    }\n    return \"\\u0436\\u0430\\u0440\\u0442\\u044B \\u043C\\u0438\\u043D\\u0443\\u0442\";\n  },\n  lessThanXMinutes: {\n    regular: {\n      one: \"1 \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      singularNominative: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      singularGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u0430\\u0437\",\n      pluralGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u0430\\u0437\"\n    },\n    future: {\n      one: \"\\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u043C \",\n      singularNominative: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u043C\",\n      singularGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u043C\",\n      pluralGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u043C\"\n    }\n  },\n  xMinutes: {\n    regular: {\n      singularNominative: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\",\n      singularGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\",\n      pluralGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\"\n    },\n    past: {\n      singularNominative: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442 \\u0431\\u04B1\\u0440\\u044B\\u043D\",\n      singularGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442 \\u0431\\u04B1\\u0440\\u044B\\u043D\",\n      pluralGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442 \\u0431\\u04B1\\u0440\\u044B\\u043D\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u043C\\u0438\\u043D\\u0443\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  aboutXHours: {\n    regular: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\"\n    },\n    future: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\\u0442\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  xHours: {\n    regular: {\n      singularNominative: \"{{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\",\n      singularGenitive: \"{{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\",\n      pluralGenitive: \"{{count}} \\u0441\\u0430\\u0493\\u0430\\u0442\"\n    }\n  },\n  xDays: {\n    regular: {\n      singularNominative: \"{{count}} \\u043A\\u04AF\\u043D\",\n      singularGenitive: \"{{count}} \\u043A\\u04AF\\u043D\",\n      pluralGenitive: \"{{count}} \\u043A\\u04AF\\u043D\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u043A\\u04AF\\u043D\\u043D\\u0435\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u043A\\u04AF\\u043D\\u043D\\u0435\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u043A\\u04AF\\u043D\\u043D\\u0435\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  aboutXWeeks: {\n    type: \"weeks\",\n    one: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D 1 \\u0430\\u043F\\u0442\\u0430\",\n    other: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u043F\\u0442\\u0430\"\n  },\n  xWeeks: {\n    type: \"weeks\",\n    one: \"1 \\u0430\\u043F\\u0442\\u0430\",\n    other: \"{{count}} \\u0430\\u043F\\u0442\\u0430\"\n  },\n  aboutXMonths: {\n    regular: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\"\n    },\n    future: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0430\\u0439\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  xMonths: {\n    regular: {\n      singularNominative: \"{{count}} \\u0430\\u0439\",\n      singularGenitive: \"{{count}} \\u0430\\u0439\",\n      pluralGenitive: \"{{count}} \\u0430\\u0439\"\n    }\n  },\n  aboutXYears: {\n    regular: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\"\n    },\n    future: {\n      singularNominative: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"\\u0448\\u0430\\u043C\\u0430\\u043C\\u0435\\u043D {{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  xYears: {\n    regular: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  },\n  overXYears: {\n    regular: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u0430\\u0441\\u0442\\u0430\\u043C\"\n    }\n  },\n  almostXYears: {\n    regular: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\\u0493\\u0430 \\u0436\\u0430\\u049B\\u044B\\u043D\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0493\\u0430 \\u0436\\u0430\\u049B\\u044B\\u043D\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0493\\u0430 \\u0436\\u0430\\u049B\\u044B\\u043D\"\n    },\n    future: {\n      singularNominative: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      singularGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\",\n      pluralGenitive: \"{{count}} \\u0436\\u044B\\u043B\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D\"\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"function\")\n  return tokenValue(options);\n  if (tokenValue.type === \"weeks\") {\n    return count === 1 ? tokenValue.one : tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" \\u043A\\u0435\\u0439\\u0456\\u043D\";\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" \\u0431\\u04B1\\u0440\\u044B\\u043D\";\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/kk/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, do MMMM y '\\u0436.'\",\n  long: \"do MMMM y '\\u0436.'\",\n  medium: \"d MMM y '\\u0436.'\",\n  short: \"dd.MM.yyyy\"\n};\nvar timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\"\n};\nvar dateTimeFormats = {\n  any: \"{{date}}, {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\"\n  })\n};\n\n// lib/constants.js\nvar daysInWeek = 7;\nvar daysInYear = 365.2425;\nvar maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\nvar minTime = -maxTime;\nvar millisecondsInWeek = 604800000;\nvar millisecondsInDay = 86400000;\nvar millisecondsInMinute = 60000;\nvar millisecondsInHour = 3600000;\nvar millisecondsInSecond = 1000;\nvar minutesInYear = 525600;\nvar minutesInMonth = 43200;\nvar minutesInDay = 1440;\nvar minutesInHour = 60;\nvar monthsInQuarter = 3;\nvar monthsInYear = 12;\nvar quartersInYear = 4;\nvar secondsInHour = 3600;\nvar secondsInMinute = 60;\nvar secondsInDay = secondsInHour * 24;\nvar secondsInWeek = secondsInDay * 7;\nvar secondsInYear = secondsInDay * daysInYear;\nvar secondsInMonth = secondsInYear / 12;\nvar secondsInQuarter = secondsInMonth * 3;\nvar constructFromSymbol = Symbol.for(\"constructDateFrom\");\n\n// lib/constructFrom.js\nfunction constructFrom(date, value) {\n  if (typeof date === \"function\")\n  return date(value);\n  if (date && _typeof(date) === \"object\" && constructFromSymbol in date)\n  return date[constructFromSymbol](value);\n  if (date instanceof Date)\n  return new date.constructor(value);\n  return new Date(value);\n}\n\n// lib/_lib/normalizeDates.js\nfunction normalizeDates(context) {for (var _len = arguments.length, dates = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {dates[_key - 1] = arguments[_key];}\n  var normalize = constructFrom.bind(null, context || dates.find(function (date) {return _typeof(date) === \"object\";}));\n  return dates.map(normalize);\n}\n\n// lib/_lib/defaultOptions.js\nfunction getDefaultOptions() {\n  return defaultOptions;\n}\nfunction setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\nvar defaultOptions = {};\n\n// lib/toDate.js\nfunction toDate(argument, context) {\n  return constructFrom(context || argument, argument);\n}\n\n// lib/startOfWeek.js\nfunction startOfWeek(date, options) {var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _defaultOptions3$loca;\n  var defaultOptions3 = getDefaultOptions();\n  var weekStartsOn = (_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 || (_options$locale = options.locale) === null || _options$locale === void 0 || (_options$locale = _options$locale.options) === null || _options$locale === void 0 ? void 0 : _options$locale.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions3.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions3$loca = defaultOptions3.locale) === null || _defaultOptions3$loca === void 0 || (_defaultOptions3$loca = _defaultOptions3$loca.options) === null || _defaultOptions3$loca === void 0 ? void 0 : _defaultOptions3$loca.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0;\n  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);\n  var day = _date.getDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  _date.setDate(_date.getDate() - diff);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// lib/isSameWeek.js\nfunction isSameWeek(laterDate, earlierDate, options) {\n  var _normalizeDates = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates2 = _slicedToArray(_normalizeDates, 2),laterDate_ = _normalizeDates2[0],earlierDate_ = _normalizeDates2[1];\n  return +startOfWeek(laterDate_, options) === +startOfWeek(earlierDate_, options);\n}\n\n// lib/locale/kk/_lib/formatRelative.js\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\\u04E9\\u0442\\u043A\\u0435\\u043D \" + weekday + \" \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\";\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\";\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\\u043A\\u0435\\u043B\\u0435\\u0441\\u0456 \" + weekday + \" \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\";\n}\nvar accusativeWeekdays = [\n\"\\u0436\\u0435\\u043A\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\",\n\"\\u0434\\u04AF\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\",\n\"\\u0441\\u0435\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\",\n\"\\u0441\\u04D9\\u0440\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\",\n\"\\u0431\\u0435\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\",\n\"\\u0436\\u04B1\\u043C\\u0430\\u0434\\u0430\",\n\"\\u0441\\u0435\\u043D\\u0431\\u0456\\u0434\\u0435\"];\n\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'\\u043A\\u0435\\u0448\\u0435 \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\",\n  today: \"'\\u0431\\u04AF\\u0433\\u0456\\u043D \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\",\n  tomorrow: \"'\\u0435\\u0440\\u0442\\u0435\\u04A3 \\u0441\\u0430\\u0493\\u0430\\u0442' p'-\\u0434\\u0435'\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/kk/_lib/localize.js\nvar eraValues = {\n  narrow: [\"\\u0431.\\u0437.\\u0434.\", \"\\u0431.\\u0437.\"],\n  abbreviated: [\"\\u0431.\\u0437.\\u0434.\", \"\\u0431.\\u0437.\"],\n  wide: [\"\\u0431\\u0456\\u0437\\u0434\\u0456\\u04A3 \\u0437\\u0430\\u043C\\u0430\\u043D\\u044B\\u043C\\u044B\\u0437\\u0493\\u0430 \\u0434\\u0435\\u0439\\u0456\\u043D\", \"\\u0431\\u0456\\u0437\\u0434\\u0456\\u04A3 \\u0437\\u0430\\u043C\\u0430\\u043D\\u044B\\u043C\\u044B\\u0437\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-\\u0448\\u0456 \\u0442\\u043E\\u049B.\", \"2-\\u0448\\u0456 \\u0442\\u043E\\u049B.\", \"3-\\u0448\\u0456 \\u0442\\u043E\\u049B.\", \"4-\\u0448\\u0456 \\u0442\\u043E\\u049B.\"],\n  wide: [\"1-\\u0448\\u0456 \\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\", \"2-\\u0448\\u0456 \\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\", \"3-\\u0448\\u0456 \\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\", \"4-\\u0448\\u0456 \\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\"]\n};\nvar monthValues = {\n  narrow: [\"\\u049A\", \"\\u0410\", \"\\u041D\", \"\\u0421\", \"\\u041C\", \"\\u041C\", \"\\u0428\", \"\\u0422\", \"\\u049A\", \"\\u049A\", \"\\u049A\", \"\\u0416\"],\n  abbreviated: [\n  \"\\u049B\\u0430\\u04A3\",\n  \"\\u0430\\u049B\\u043F\",\n  \"\\u043D\\u0430\\u0443\",\n  \"\\u0441\\u04D9\\u0443\",\n  \"\\u043C\\u0430\\u043C\",\n  \"\\u043C\\u0430\\u0443\",\n  \"\\u0448\\u0456\\u043B\",\n  \"\\u0442\\u0430\\u043C\",\n  \"\\u049B\\u044B\\u0440\",\n  \"\\u049B\\u0430\\u0437\",\n  \"\\u049B\\u0430\\u0440\",\n  \"\\u0436\\u0435\\u043B\"],\n\n  wide: [\n  \"\\u049B\\u0430\\u04A3\\u0442\\u0430\\u0440\",\n  \"\\u0430\\u049B\\u043F\\u0430\\u043D\",\n  \"\\u043D\\u0430\\u0443\\u0440\\u044B\\u0437\",\n  \"\\u0441\\u04D9\\u0443\\u0456\\u0440\",\n  \"\\u043C\\u0430\\u043C\\u044B\\u0440\",\n  \"\\u043C\\u0430\\u0443\\u0441\\u044B\\u043C\",\n  \"\\u0448\\u0456\\u043B\\u0434\\u0435\",\n  \"\\u0442\\u0430\\u043C\\u044B\\u0437\",\n  \"\\u049B\\u044B\\u0440\\u043A\\u04AF\\u0439\\u0435\\u043A\",\n  \"\\u049B\\u0430\\u0437\\u0430\\u043D\",\n  \"\\u049B\\u0430\\u0440\\u0430\\u0448\\u0430\",\n  \"\\u0436\\u0435\\u043B\\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\"]\n\n};\nvar formattingMonthValues = {\n  narrow: [\"\\u049A\", \"\\u0410\", \"\\u041D\", \"\\u0421\", \"\\u041C\", \"\\u041C\", \"\\u0428\", \"\\u0422\", \"\\u049A\", \"\\u049A\", \"\\u049A\", \"\\u0416\"],\n  abbreviated: [\n  \"\\u049B\\u0430\\u04A3\",\n  \"\\u0430\\u049B\\u043F\",\n  \"\\u043D\\u0430\\u0443\",\n  \"\\u0441\\u04D9\\u0443\",\n  \"\\u043C\\u0430\\u043C\",\n  \"\\u043C\\u0430\\u0443\",\n  \"\\u0448\\u0456\\u043B\",\n  \"\\u0442\\u0430\\u043C\",\n  \"\\u049B\\u044B\\u0440\",\n  \"\\u049B\\u0430\\u0437\",\n  \"\\u049B\\u0430\\u0440\",\n  \"\\u0436\\u0435\\u043B\"],\n\n  wide: [\n  \"\\u049B\\u0430\\u04A3\\u0442\\u0430\\u0440\",\n  \"\\u0430\\u049B\\u043F\\u0430\\u043D\",\n  \"\\u043D\\u0430\\u0443\\u0440\\u044B\\u0437\",\n  \"\\u0441\\u04D9\\u0443\\u0456\\u0440\",\n  \"\\u043C\\u0430\\u043C\\u044B\\u0440\",\n  \"\\u043C\\u0430\\u0443\\u0441\\u044B\\u043C\",\n  \"\\u0448\\u0456\\u043B\\u0434\\u0435\",\n  \"\\u0442\\u0430\\u043C\\u044B\\u0437\",\n  \"\\u049B\\u044B\\u0440\\u043A\\u04AF\\u0439\\u0435\\u043A\",\n  \"\\u049B\\u0430\\u0437\\u0430\\u043D\",\n  \"\\u049B\\u0430\\u0440\\u0430\\u0448\\u0430\",\n  \"\\u0436\\u0435\\u043B\\u0442\\u043E\\u049B\\u0441\\u0430\\u043D\"]\n\n};\nvar dayValues = {\n  narrow: [\"\\u0416\", \"\\u0414\", \"\\u0421\", \"\\u0421\", \"\\u0411\", \"\\u0416\", \"\\u0421\"],\n  short: [\"\\u0436\\u0441\", \"\\u0434\\u0441\", \"\\u0441\\u0441\", \"\\u0441\\u0440\", \"\\u0431\\u0441\", \"\\u0436\\u043C\", \"\\u0441\\u0431\"],\n  abbreviated: [\"\\u0436\\u0441\", \"\\u0434\\u0441\", \"\\u0441\\u0441\", \"\\u0441\\u0440\", \"\\u0431\\u0441\", \"\\u0436\\u043C\", \"\\u0441\\u0431\"],\n  wide: [\n  \"\\u0436\\u0435\\u043A\\u0441\\u0435\\u043D\\u0431\\u0456\",\n  \"\\u0434\\u04AF\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\",\n  \"\\u0441\\u0435\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\",\n  \"\\u0441\\u04D9\\u0440\\u0441\\u0435\\u043D\\u0431\\u0456\",\n  \"\\u0431\\u0435\\u0439\\u0441\\u0435\\u043D\\u0431\\u0456\",\n  \"\\u0436\\u04B1\\u043C\\u0430\",\n  \"\\u0441\\u0435\\u043D\\u0431\\u0456\"]\n\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"\\u0422\\u0414\",\n    pm: \"\\u0422\\u041A\",\n    midnight: \"\\u0442\\u04AF\\u043D \\u043E\\u0440\\u0442\\u0430\\u0441\\u044B\",\n    noon: \"\\u0442\\u04AF\\u0441\",\n    morning: \"\\u0442\\u0430\\u04A3\",\n    afternoon: \"\\u043A\\u04AF\\u043D\\u0434\\u0456\\u0437\",\n    evening: \"\\u043A\\u0435\\u0448\",\n    night: \"\\u0442\\u04AF\\u043D\"\n  },\n  wide: {\n    am: \"\\u0422\\u0414\",\n    pm: \"\\u0422\\u041A\",\n    midnight: \"\\u0442\\u04AF\\u043D \\u043E\\u0440\\u0442\\u0430\\u0441\\u044B\",\n    noon: \"\\u0442\\u04AF\\u0441\",\n    morning: \"\\u0442\\u0430\\u04A3\",\n    afternoon: \"\\u043A\\u04AF\\u043D\\u0434\\u0456\\u0437\",\n    evening: \"\\u043A\\u0435\\u0448\",\n    night: \"\\u0442\\u04AF\\u043D\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"\\u0422\\u0414\",\n    pm: \"\\u0422\\u041A\",\n    midnight: \"\\u0442\\u04AF\\u043D \\u043E\\u0440\\u0442\\u0430\\u0441\\u044B\\u043D\\u0434\\u0430\",\n    noon: \"\\u0442\\u04AF\\u0441\",\n    morning: \"\\u0442\\u0430\\u04A3\",\n    afternoon: \"\\u043A\\u04AF\\u043D\",\n    evening: \"\\u043A\\u0435\\u0448\",\n    night: \"\\u0442\\u04AF\\u043D\"\n  },\n  wide: {\n    am: \"\\u0422\\u0414\",\n    pm: \"\\u0422\\u041A\",\n    midnight: \"\\u0442\\u04AF\\u043D \\u043E\\u0440\\u0442\\u0430\\u0441\\u044B\\u043D\\u0434\\u0430\",\n    noon: \"\\u0442\\u04AF\\u0441\\u0442\\u0435\",\n    morning: \"\\u0442\\u0430\\u04A3\\u0435\\u0440\\u0442\\u0435\\u04A3\",\n    afternoon: \"\\u043A\\u04AF\\u043D\\u0434\\u0456\\u0437\",\n    evening: \"\\u043A\\u0435\\u0448\\u0442\\u0435\",\n    night: \"\\u0442\\u04AF\\u043D\\u0434\\u0435\"\n  }\n};\nvar suffixes = {\n  0: \"-\\u0448\\u0456\",\n  1: \"-\\u0448\\u0456\",\n  2: \"-\\u0448\\u0456\",\n  3: \"-\\u0448\\u0456\",\n  4: \"-\\u0448\\u0456\",\n  5: \"-\\u0448\\u0456\",\n  6: \"-\\u0448\\u044B\",\n  7: \"-\\u0448\\u0456\",\n  8: \"-\\u0448\\u0456\",\n  9: \"-\\u0448\\u044B\",\n  10: \"-\\u0448\\u044B\",\n  20: \"-\\u0448\\u044B\",\n  30: \"-\\u0448\\u044B\",\n  40: \"-\\u0448\\u044B\",\n  50: \"-\\u0448\\u0456\",\n  60: \"-\\u0448\\u044B\",\n  70: \"-\\u0448\\u0456\",\n  80: \"-\\u0448\\u0456\",\n  90: \"-\\u0448\\u044B\",\n  100: \"-\\u0448\\u0456\"\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var mod10 = number % 10;\n  var b = number >= 100 ? 100 : null;\n  var suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || \"\";\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/kk/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(ші|шы))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((б )?з\\.?\\s?д\\.?)/i,\n  abbreviated: /^((б )?з\\.?\\s?д\\.?)/i,\n  wide: /^(біздің заманымызға дейін|біздің заманымыз|біздің заманымыздан)/i\n};\nvar parseEraPatterns = {\n  any: [/^б/i, /^з/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?ші)? тоқ.?/i,\n  wide: /^[1234](-?ші)? тоқсан/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(қ|а|н|с|м|мау|ш|т|қыр|қаз|қар|ж)/i,\n  abbreviated: /^(қаң|ақп|нау|сәу|мам|мау|шіл|там|қыр|қаз|қар|жел)/i,\n  wide: /^(қаңтар|ақпан|наурыз|сәуір|мамыр|маусым|шілде|тамыз|қыркүйек|қазан|қараша|желтоқсан)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /^қ/i,\n  /^а/i,\n  /^н/i,\n  /^с/i,\n  /^м/i,\n  /^м/i,\n  /^ш/i,\n  /^т/i,\n  /^қ/i,\n  /^қ/i,\n  /^қ/i,\n  /^ж/i],\n\n  abbreviated: [\n  /^қаң/i,\n  /^ақп/i,\n  /^нау/i,\n  /^сәу/i,\n  /^мам/i,\n  /^мау/i,\n  /^шіл/i,\n  /^там/i,\n  /^қыр/i,\n  /^қаз/i,\n  /^қар/i,\n  /^жел/i],\n\n  any: [\n  /^қ/i,\n  /^а/i,\n  /^н/i,\n  /^с/i,\n  /^м/i,\n  /^м/i,\n  /^ш/i,\n  /^т/i,\n  /^қ/i,\n  /^қ/i,\n  /^қ/i,\n  /^ж/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^(ж|д|с|с|б|ж|с)/i,\n  short: /^(жс|дс|сс|ср|бс|жм|сб)/i,\n  wide: /^(жексенбі|дүйсенбі|сейсенбі|сәрсенбі|бейсенбі|жұма|сенбі)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ж/i, /^д/i, /^с/i, /^с/i, /^б/i, /^ж/i, /^с/i],\n  short: [/^жс/i, /^дс/i, /^сс/i, /^ср/i, /^бс/i, /^жм/i, /^сб/i],\n  any: [\n  /^ж[ек]/i,\n  /^д[үй]/i,\n  /^сe[й]/i,\n  /^сә[р]/i,\n  /^б[ей]/i,\n  /^ж[ұм]/i,\n  /^се[н]/i]\n\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  wide: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  any: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ТД/i,\n    pm: /^ТК/i,\n    midnight: /^түн орта/i,\n    noon: /^күндіз/i,\n    morning: /таң/i,\n    afternoon: /түс/i,\n    evening: /кеш/i,\n    night: /түн/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/kk.js\nvar kk = {\n  code: \"kk\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// lib/locale/kk/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    kk: kk }) });\n\n\n\n//# debugId=CE0108605B17309A64756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,SAAS,CAAc,CAAC,EAAK,EAAG,CAAC,OAAO,EAAgB,CAAG,GAAK,EAAsB,EAAK,CAAC,GAAK,EAA4B,EAAK,CAAC,GAAK,EAAiB,EAAG,SAAS,CAAgB,EAAG,CAAC,MAAM,IAAI,UAAU,2IAA2I,EAAG,SAAS,CAA2B,CAAC,EAAG,EAAQ,CAAC,IAAK,EAAG,OAAO,UAAW,IAAM,SAAU,OAAO,EAAkB,EAAG,CAAM,EAAE,IAAI,EAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,EAAG,EAAE,EAAE,GAAI,IAAM,UAAY,EAAE,YAAa,EAAI,EAAE,YAAY,KAAK,GAAI,IAAM,OAAS,IAAM,MAAO,OAAO,MAAM,KAAK,CAAC,EAAE,GAAI,IAAM,aAAe,2CAA2C,KAAK,CAAC,EAAG,OAAO,EAAkB,EAAG,CAAM,EAAG,SAAS,CAAiB,CAAC,EAAK,EAAK,CAAC,GAAI,GAAO,MAAQ,EAAM,EAAI,OAAQ,EAAM,EAAI,OAAO,QAAS,EAAI,EAAG,EAAO,IAAI,MAAM,CAAG,EAAG,EAAI,EAAK,IAAK,EAAK,GAAK,EAAI,GAAG,OAAO,EAAM,SAAS,CAAqB,CAAC,EAAG,EAAG,CAAC,IAAI,EAAY,GAAR,KAAY,YAA6B,QAAtB,aAAgC,EAAE,OAAO,WAAa,EAAE,cAAc,GAAY,GAAR,KAAW,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAI,CAAC,EAAE,EAAI,GAAG,EAAI,GAAG,GAAI,CAAC,GAAI,GAAK,EAAI,EAAE,KAAK,CAAC,GAAG,KAAY,IAAN,EAAS,CAAC,GAAI,OAAO,CAAC,IAAM,EAAG,OAAO,EAAI,OAAU,QAAS,GAAK,EAAI,EAAE,KAAK,CAAC,GAAG,QAAU,EAAE,KAAK,EAAE,KAAK,EAAG,EAAE,SAAW,GAAI,EAAI,WAAa,EAAP,CAAW,EAAI,GAAI,EAAI,SAAI,CAAS,GAAI,CAAC,IAAK,GAAa,EAAE,QAAV,OAAqB,EAAI,EAAE,OAAO,EAAG,OAAO,CAAC,IAAM,GAAI,cAAS,CAAS,GAAI,EAAG,MAAM,GAAI,OAAO,GAAI,SAAS,CAAe,CAAC,EAAK,CAAC,GAAI,MAAM,QAAQ,CAAG,EAAG,OAAO,EAAK,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACrmG,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIH,SAAS,CAAU,CAAC,EAAQ,EAAO,CACjC,GAAI,EAAO,KAAO,IAAU,EAC5B,OAAO,EAAO,IACd,IAAI,EAAQ,EAAQ,GAChB,EAAS,EAAQ,IACrB,GAAI,IAAU,GAAK,IAAW,GAC5B,OAAO,EAAO,mBAAmB,QAAQ,YAAa,OAAO,CAAK,CAAC,UAC1D,GAAS,GAAK,GAAS,IAAM,EAAS,IAAM,EAAS,IAC9D,OAAO,EAAO,iBAAiB,QAAQ,YAAa,OAAO,CAAK,CAAC,MAEjE,QAAO,EAAO,eAAe,QAAQ,YAAa,OAAO,CAAK,CAAC,EAGnE,IAAI,EAAuB,CACzB,iBAAkB,CAChB,QAAS,CACP,IAAK,wEACL,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,EACA,OAAQ,CACN,IAAK,2GACL,mBAAoB,kGACpB,iBAAkB,kGAClB,eAAgB,iGAClB,CACF,EACA,SAAU,CACR,QAAS,CACP,mBAAoB,iDACpB,iBAAkB,iDAClB,eAAgB,gDAClB,EACA,KAAM,CACJ,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,EACA,OAAQ,CACN,mBAAoB,kGACpB,iBAAkB,kGAClB,eAAgB,iGAClB,CACF,EACA,qBAAsB,CAAW,CAAC,EAAS,CACzC,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,MAAO,yGAEP,OAAO,+FAGX,MAAO,iEAET,iBAAkB,CAChB,QAAS,CACP,IAAK,kEACL,mBAAoB,0EACpB,iBAAkB,0EAClB,eAAgB,yEAClB,EACA,OAAQ,CACN,IAAK,uEACL,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,CACF,EACA,SAAU,CACR,QAAS,CACP,mBAAoB,2CACpB,iBAAkB,2CAClB,eAAgB,0CAClB,EACA,KAAM,CACJ,mBAAoB,0EACpB,iBAAkB,0EAClB,eAAgB,yEAClB,EACA,OAAQ,CACN,mBAAoB,4FACpB,iBAAkB,4FAClB,eAAgB,2FAClB,CACF,EACA,YAAa,CACX,QAAS,CACP,mBAAoB,sFACpB,iBAAkB,sFAClB,eAAgB,qFAClB,EACA,OAAQ,CACN,mBAAoB,uIACpB,iBAAkB,uIAClB,eAAgB,sIAClB,CACF,EACA,OAAQ,CACN,QAAS,CACP,mBAAoB,2CACpB,iBAAkB,2CAClB,eAAgB,0CAClB,CACF,EACA,MAAO,CACL,QAAS,CACP,mBAAoB,+BACpB,iBAAkB,+BAClB,eAAgB,8BAClB,EACA,OAAQ,CACN,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,CACF,EACA,YAAa,CACX,KAAM,QACN,IAAK,wEACL,MAAO,+EACT,EACA,OAAQ,CACN,KAAM,QACN,IAAK,6BACL,MAAO,oCACT,EACA,aAAc,CACZ,QAAS,CACP,mBAAoB,oEACpB,iBAAkB,oEAClB,eAAgB,mEAClB,EACA,OAAQ,CACN,mBAAoB,qHACpB,iBAAkB,qHAClB,eAAgB,oHAClB,CACF,EACA,QAAS,CACP,QAAS,CACP,mBAAoB,yBACpB,iBAAkB,yBAClB,eAAgB,wBAClB,CACF,EACA,YAAa,CACX,QAAS,CACP,mBAAoB,0EACpB,iBAAkB,0EAClB,eAAgB,yEAClB,EACA,OAAQ,CACN,mBAAoB,2HACpB,iBAAkB,2HAClB,eAAgB,0HAClB,CACF,EACA,OAAQ,CACN,QAAS,CACP,mBAAoB,+BACpB,iBAAkB,+BAClB,eAAgB,8BAClB,EACA,OAAQ,CACN,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,CACF,EACA,WAAY,CACV,QAAS,CACP,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,EACA,OAAQ,CACN,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,CACF,EACA,aAAc,CACZ,QAAS,CACP,mBAAoB,0EACpB,iBAAkB,0EAClB,eAAgB,yEAClB,EACA,OAAQ,CACN,mBAAoB,gFACpB,iBAAkB,gFAClB,eAAgB,+EAClB,CACF,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EAAa,EAAqB,GACtC,UAAW,IAAe,WAC1B,OAAO,EAAW,CAAO,EACzB,GAAI,EAAW,OAAS,QACtB,OAAO,IAAU,EAAI,EAAW,IAAM,EAAW,MAAM,QAAQ,YAAa,OAAO,CAAK,CAAC,EAE3F,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,GAAI,EAAW,OACb,OAAO,EAAW,EAAW,OAAQ,CAAK,MAE1C,QAAO,EAAW,EAAW,QAAS,CAAK,EAAI,0CAG7C,EAAW,KACb,OAAO,EAAW,EAAW,KAAM,CAAK,MAExC,QAAO,EAAW,EAAW,QAAS,CAAK,EAAI,sCAInD,QAAO,EAAW,EAAW,QAAS,CAAK,GAK/C,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,4BACN,KAAM,sBACN,OAAQ,oBACR,MAAO,YACT,EACI,EAAc,CAChB,KAAM,eACN,KAAM,YACN,OAAQ,UACR,MAAO,MACT,EACI,EAAkB,CACpB,IAAK,oBACP,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,KAChB,CAAC,CACH,EAGI,GAAa,EACb,EAAa,SACb,EAAU,KAAK,IAAI,GAAI,CAAC,EAAI,GAAK,GAAK,GAAK,KAC3C,IAAW,EACX,GAAqB,UACrB,GAAoB,SACpB,GAAuB,MACvB,GAAqB,QACrB,GAAuB,KACvB,GAAgB,OAChB,GAAiB,MACjB,GAAe,KACf,GAAgB,GAChB,GAAkB,EAClB,GAAe,GACf,GAAiB,EACjB,EAAgB,KAChB,GAAkB,GAClB,EAAe,EAAgB,GAC/B,GAAgB,EAAe,EAC/B,EAAgB,EAAe,EAC/B,EAAiB,EAAgB,GACjC,GAAmB,EAAiB,EACpC,EAAsB,OAAO,IAAI,mBAAmB,EAGxD,SAAS,CAAa,CAAC,EAAM,EAAO,CAClC,UAAW,IAAS,WACpB,OAAO,EAAK,CAAK,EACjB,GAAI,GAAQ,EAAQ,CAAI,IAAM,UAAY,KAAuB,EACjE,OAAO,EAAK,GAAqB,CAAK,EACtC,GAAI,aAAgB,KACpB,OAAO,IAAI,EAAK,YAAY,CAAK,EACjC,OAAO,IAAI,KAAK,CAAK,EAIvB,SAAS,CAAc,CAAC,EAAS,CAAC,QAAS,EAAO,UAAU,OAAQ,EAAQ,IAAI,MAAM,EAAO,EAAI,EAAO,EAAI,CAAC,EAAG,EAAO,EAAG,EAAO,EAAM,IAAS,EAAM,EAAO,GAAK,UAAU,GAC1K,IAAI,EAAY,EAAc,KAAK,KAAM,GAAW,EAAM,aAAc,CAAC,EAAM,CAAC,OAAO,EAAQ,CAAI,IAAM,SAAU,CAAC,EACpH,OAAO,EAAM,IAAI,CAAS,EAI5B,SAAS,CAAiB,EAAG,CAC3B,OAAO,EAET,SAAS,EAAiB,CAAC,EAAY,CACrC,EAAiB,EAEnB,IAAI,EAAiB,CAAC,EAGtB,SAAS,CAAM,CAAC,EAAU,EAAS,CACjC,OAAO,EAAc,GAAW,EAAU,CAAQ,EAIpD,SAAS,CAAW,CAAC,EAAM,EAAS,CAAC,IAAI,EAAM,EAAO,EAAO,EAAuB,EAAiB,EAC/F,EAAkB,EAAkB,EACpC,GAAgB,GAAQ,GAAS,GAAS,EAAwB,IAAY,MAAQ,IAAiB,OAAS,OAAI,EAAQ,gBAAkB,MAAQ,IAA+B,OAAI,EAAwB,IAAY,MAAQ,IAAiB,SAAM,EAAkB,EAAQ,UAAY,MAAQ,IAAyB,SAAM,EAAkB,EAAgB,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,gBAAkB,MAAQ,IAAe,OAAI,EAAQ,EAAgB,gBAAkB,MAAQ,IAAe,OAAI,GAAS,EAAwB,EAAgB,UAAY,MAAQ,IAA+B,SAAM,EAAwB,EAAsB,WAAa,MAAQ,IAA+B,OAAS,OAAI,EAAsB,gBAAkB,MAAQ,IAAc,OAAI,EAAO,EAC10B,EAAQ,EAAO,EAAM,IAAY,MAAQ,IAAiB,OAAS,OAAI,EAAQ,EAAE,EACjF,EAAM,EAAM,OAAO,EACnB,IAAQ,EAAM,EAAe,EAAI,GAAK,EAAM,EAGhD,OAFA,EAAM,QAAQ,EAAM,QAAQ,EAAI,EAAI,EACpC,EAAM,SAAS,EAAG,EAAG,EAAG,CAAC,EAClB,EAIT,SAAS,CAAU,CAAC,EAAW,EAAa,EAAS,CACnD,IAAI,EAAkB,EAAe,IAAY,MAAQ,IAAiB,OAAS,OAAI,EAAQ,GAAI,EAAW,CAAW,EAAE,EAAmB,EAAe,EAAiB,CAAC,EAAE,EAAa,EAAiB,GAAG,EAAe,EAAiB,GAClP,OAAQ,EAAY,EAAY,CAAO,KAAO,EAAY,EAAc,CAAO,EAIjF,SAAS,CAAS,CAAC,EAAK,CACtB,IAAI,EAAU,EAAmB,GACjC,MAAO,mCAAqC,EAAU,oDAExD,SAAS,CAAQ,CAAC,EAAK,CACrB,IAAI,EAAU,EAAmB,GACjC,MAAO,IAAM,EAAU,oDAEzB,SAAS,EAAS,CAAC,EAAK,CACtB,IAAI,EAAU,EAAmB,GACjC,MAAO,yCAA2C,EAAU,oDAE9D,IAAI,EAAqB,CACzB,+DACA,+DACA,+DACA,+DACA,+DACA,uCACA,4CAA4C,EAExC,GAAuB,CACzB,kBAAmB,CAAQ,CAAC,EAAM,EAAU,EAAS,CACnD,IAAI,EAAM,EAAK,OAAO,EACtB,GAAI,EAAW,EAAM,EAAU,CAAO,EACpC,OAAO,EAAS,CAAG,MAEnB,QAAO,EAAU,CAAG,GAGxB,UAAW,6EACX,MAAO,mFACP,SAAU,mFACV,kBAAmB,CAAQ,CAAC,EAAM,EAAU,EAAS,CACnD,IAAI,EAAM,EAAK,OAAO,EACtB,GAAI,EAAW,EAAM,EAAU,CAAO,EACpC,OAAO,EAAS,CAAG,MAEnB,QAAO,GAAU,CAAG,GAGxB,MAAO,GACT,EACI,YAA0B,CAAc,CAAC,EAAO,EAAM,EAAU,EAAS,CAC3E,IAAI,EAAS,GAAqB,GAClC,UAAW,IAAW,WACpB,OAAO,EAAO,EAAM,EAAU,CAAO,EAEvC,OAAO,GAIT,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,GAAY,CACd,OAAQ,CAAC,wBAAyB,gBAAgB,EAClD,YAAa,CAAC,wBAAyB,gBAAgB,EACvD,KAAM,CAAC,yIAA0I,6FAA6F,CAChP,EACI,GAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,qCAAsC,qCAAsC,qCAAsC,oCAAoC,EACpK,KAAM,CAAC,sDAAuD,sDAAuD,sDAAuD,qDAAqD,CACnO,EACI,GAAc,CAChB,OAAQ,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,QAAQ,EAC/H,YAAa,CACb,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,oBAAoB,EAEpB,KAAM,CACN,uCACA,iCACA,uCACA,iCACA,iCACA,uCACA,iCACA,iCACA,mDACA,iCACA,uCACA,wDAAwD,CAE1D,EACI,GAAwB,CAC1B,OAAQ,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,QAAQ,EAC/H,YAAa,CACb,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,oBAAoB,EAEpB,KAAM,CACN,uCACA,iCACA,uCACA,iCACA,iCACA,uCACA,iCACA,iCACA,mDACA,iCACA,uCACA,wDAAwD,CAE1D,EACI,GAAY,CACd,OAAQ,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,QAAQ,EAC7E,MAAO,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,cAAc,EACtH,YAAa,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,cAAc,EAC5H,KAAM,CACN,mDACA,mDACA,mDACA,mDACA,mDACA,2BACA,gCAAgC,CAElC,EACI,GAAkB,CACpB,OAAQ,CACN,GAAI,eACJ,GAAI,eACJ,SAAU,0DACV,KAAM,qBACN,QAAS,qBACT,UAAW,uCACX,QAAS,qBACT,MAAO,oBACT,EACA,KAAM,CACJ,GAAI,eACJ,GAAI,eACJ,SAAU,0DACV,KAAM,qBACN,QAAS,qBACT,UAAW,uCACX,QAAS,qBACT,MAAO,oBACT,CACF,EACI,GAA4B,CAC9B,OAAQ,CACN,GAAI,eACJ,GAAI,eACJ,SAAU,4EACV,KAAM,qBACN,QAAS,qBACT,UAAW,qBACX,QAAS,qBACT,MAAO,oBACT,EACA,KAAM,CACJ,GAAI,eACJ,GAAI,eACJ,SAAU,4EACV,KAAM,iCACN,QAAS,mDACT,UAAW,uCACX,QAAS,iCACT,MAAO,gCACT,CACF,EACI,EAAW,CACb,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,EAAG,gBACH,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,GAAI,gBACJ,IAAK,eACP,EACI,YAAyB,CAAa,CAAC,EAAa,EAAU,CAChE,IAAI,EAAS,OAAO,CAAW,EAC3B,EAAQ,EAAS,GACjB,EAAI,GAAU,IAAM,IAAM,KAC1B,EAAS,EAAS,IAAW,EAAS,IAAU,GAAK,EAAS,IAAM,GACxE,OAAO,EAAS,GAEd,GAAW,CACb,cAAe,GACf,IAAK,EAAgB,CACnB,OAAQ,GACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,GACR,aAAc,OACd,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,GACR,aAAc,OACd,iBAAkB,GAClB,uBAAwB,MAC1B,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,GACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,GACR,aAAc,MACd,iBAAkB,GAClB,uBAAwB,MAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,GAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,GAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAGtC,SAAS,EAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,EAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,EAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,GAA4B,sBAC5B,GAA4B,OAC5B,GAAmB,CACrB,OAAQ,uBACR,YAAa,uBACb,KAAM,mEACR,EACI,GAAmB,CACrB,IAAK,CAAC,MAAM,KAAK,CACnB,EACI,GAAuB,CACzB,OAAQ,WACR,YAAa,wBACb,KAAM,wBACR,EACI,GAAuB,CACzB,IAAK,CAAC,KAAM,KAAM,KAAM,IAAI,CAC9B,EACI,GAAqB,CACvB,OAAQ,sCACR,YAAa,sDACb,KAAM,wFACR,EACI,GAAqB,CACvB,OAAQ,CACR,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KAAI,EAEJ,YAAa,CACb,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,OAAM,EAEN,IAAK,CACL,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KAAI,CAEN,EACI,GAAmB,CACrB,OAAQ,oBACR,MAAO,2BACP,KAAM,6DACR,EACI,GAAmB,CACrB,OAAQ,CAAC,MAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EACvD,MAAO,CAAC,OAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAM,EAC7D,IAAK,CACL,UACA,UACA,UACA,UACA,UACA,UACA,SAAQ,CAEV,EACI,GAAyB,CAC3B,OAAQ,gHACR,KAAM,gHACN,IAAK,+GACP,EACI,GAAyB,CAC3B,IAAK,CACH,GAAI,OACJ,GAAI,OACJ,SAAU,aACV,KAAM,WACN,QAAS,OACT,UAAW,OACX,QAAS,OACT,MAAO,MACT,CACF,EACI,GAAQ,CACV,cAAe,GAAoB,CACjC,aAAc,GACd,aAAc,GACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,GACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,GACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,GACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,GACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,GACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAK,CACP,KAAM,KACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,GAChB,SAAU,GACV,MAAO,GACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,GAAI,EAAG,CAAC,CAAE,CAAC,IAOZ", "debugId": "CBB89493BFC262CF64756E2164756E21", "names": []}