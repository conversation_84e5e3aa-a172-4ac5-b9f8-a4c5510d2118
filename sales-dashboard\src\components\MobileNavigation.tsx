'use client';

import { Bars3Icon } from '@heroicons/react/24/outline';

interface MobileNavigationProps {
  currentPage: string;
  onMenuClick: () => void;
}

const pageNames = {
  dashboard: 'Dashboard',
  leads: 'Leads',
  contacts: 'Contacts',
  communications: 'Communications',
  analytics: 'Analytics',
};

export default function MobileNavigation({ currentPage, onMenuClick }: MobileNavigationProps) {
  return (
    <div className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button
            onClick={onMenuClick}
            className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors"
          >
            <Bars3Icon className="w-6 h-6" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">
            {pageNames[currentPage as keyof typeof pageNames] || 'Dashboard'}
          </h1>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">U</span>
          </div>
        </div>
      </div>
    </div>
  );
}
