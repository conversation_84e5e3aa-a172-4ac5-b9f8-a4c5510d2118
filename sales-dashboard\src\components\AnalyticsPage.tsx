'use client';

import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  AreaChart,
  Area
} from 'recharts';

// Sample data for analytics
const salesTrendData = [
  { month: 'Jan', revenue: 45000, leads: 120, conversions: 24 },
  { month: 'Feb', revenue: 52000, leads: 135, conversions: 28 },
  { month: 'Mar', revenue: 48000, leads: 110, conversions: 22 },
  { month: 'Apr', revenue: 61000, leads: 150, conversions: 35 },
  { month: 'May', revenue: 55000, leads: 140, conversions: 30 },
  { month: 'Jun', revenue: 67000, leads: 160, conversions: 38 },
];

const conversionFunnelData = [
  { stage: 'Leads', count: 1000, percentage: 100 },
  { stage: 'Qualified', count: 400, percentage: 40 },
  { stage: 'Proposal', count: 200, percentage: 20 },
  { stage: 'Negotiation', count: 100, percentage: 10 },
  { stage: 'Closed Won', count: 50, percentage: 5 },
];

const communicationEffectivenessData = [
  { type: 'Email', sent: 450, responses: 180, rate: 40 },
  { type: 'Phone', sent: 120, responses: 84, rate: 70 },
  { type: 'Text', sent: 200, responses: 140, rate: 70 },
  { type: 'Meeting', sent: 60, responses: 54, rate: 90 },
];

const leadSourceROI = [
  { source: 'Website', cost: 5000, revenue: 25000, roi: 400, color: '#0088FE' },
  { source: 'Social Media', cost: 3000, revenue: 15000, roi: 400, color: '#00C49F' },
  { source: 'Email Marketing', cost: 2000, revenue: 18000, roi: 800, color: '#FFBB28' },
  { source: 'Referrals', cost: 1000, revenue: 12000, roi: 1100, color: '#FF8042' },
];

const kpiData = [
  { 
    name: 'Conversion Rate', 
    value: '5.2%', 
    change: '+0.8%', 
    changeType: 'positive',
    description: 'Leads to customers'
  },
  { 
    name: 'Avg Deal Size', 
    value: '$12,500', 
    change: '+15.3%', 
    changeType: 'positive',
    description: 'Average revenue per deal'
  },
  { 
    name: 'Sales Cycle', 
    value: '28 days', 
    change: '-3 days', 
    changeType: 'positive',
    description: 'Average time to close'
  },
  { 
    name: 'Response Rate', 
    value: '68%', 
    change: '-2.1%', 
    changeType: 'negative',
    description: 'Communication response rate'
  },
];

export default function AnalyticsPage() {
  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Sales Analytics</h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
            <option>Last 6 months</option>
            <option>Last 3 months</option>
            <option>Last month</option>
            <option>This year</option>
          </select>
          <button className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
            <ChartBarIcon className="w-5 h-5 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {kpiData.map((kpi) => (
          <div key={kpi.name} className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">{kpi.name}</p>
                <p className="text-2xl font-bold text-gray-900">{kpi.value}</p>
                <p className="text-xs text-gray-400 mt-1">{kpi.description}</p>
              </div>
              <div className={`flex items-center text-sm font-semibold ${
                kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {kpi.changeType === 'positive' ? (
                  <ArrowUpIcon className="w-4 h-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="w-4 h-4 mr-1" />
                )}
                {kpi.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Revenue Trend */}
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
          <ResponsiveContainer width="100%" height={250}>
            <AreaChart data={salesTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />
              <Area type="monotone" dataKey="revenue" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Lead Source ROI */}
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Lead Source ROI</h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={leadSourceROI}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="source" />
              <YAxis />
              <Tooltip formatter={(value) => [`${value}%`, 'ROI']} />
              <Bar dataKey="roi" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Conversion Funnel */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Sales Conversion Funnel</h3>
        <div className="space-y-4">
          {conversionFunnelData.map((stage, index) => (
            <div key={stage.stage} className="relative">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">{stage.stage}</span>
                <span className="text-sm text-gray-500">{stage.count} ({stage.percentage}%)</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${stage.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Communication Effectiveness */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Communication Effectiveness</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={communicationEffectivenessData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="rate" fill="#8B5CF6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Lead Conversion Over Time */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Leads vs Conversions</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={salesTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="leads" stroke="#3B82F6" strokeWidth={2} />
              <Line type="monotone" dataKey="conversions" stroke="#10B981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Performance Summary Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Monthly Performance Summary</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leads</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversion Rate</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {salesTrendData.map((data) => (
                <tr key={data.month} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{data.month}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${data.revenue.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{data.leads}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{data.conversions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {((data.conversions / data.leads) * 100).toFixed(1)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
