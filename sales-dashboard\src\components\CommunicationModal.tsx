'use client';

import { useState } from 'react';
import Modal from './Modal';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  ChatBubbleLeftIcon, 
  VideoCameraIcon 
} from '@heroicons/react/24/outline';

interface CommunicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (communicationData: any) => void;
  type?: 'email' | 'call' | 'text' | 'meeting';
  contactName?: string;
}

export default function CommunicationModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  type = 'email',
  contactName = ''
}: CommunicationModalProps) {
  const [formData, setFormData] = useState({
    type: type,
    contact: contactName,
    subject: '',
    content: '',
    duration: '',
    scheduledDate: '',
    scheduledTime: ''
  });

  const typeConfig = {
    email: {
      title: 'Send Email',
      icon: EnvelopeIcon,
      color: 'blue',
      fields: ['contact', 'subject', 'content']
    },
    call: {
      title: 'Log Call',
      icon: PhoneIcon,
      color: 'green',
      fields: ['contact', 'subject', 'content', 'duration']
    },
    text: {
      title: 'Send Text Message',
      icon: ChatBubbleLeftIcon,
      color: 'purple',
      fields: ['contact', 'content']
    },
    meeting: {
      title: 'Schedule Meeting',
      icon: VideoCameraIcon,
      color: 'orange',
      fields: ['contact', 'subject', 'content', 'scheduledDate', 'scheduledTime', 'duration']
    }
  };

  const config = typeConfig[type];
  const IconComponent = config.icon;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const communicationData = {
      ...formData,
      id: Date.now(),
      timestamp: type === 'meeting' && formData.scheduledDate 
        ? `${formData.scheduledDate}T${formData.scheduledTime}:00`
        : new Date().toISOString(),
      status: type === 'meeting' ? 'scheduled' : 'sent',
      duration: formData.duration ? parseInt(formData.duration) : undefined
    };
    onSubmit(communicationData);
    setFormData({
      type: type,
      contact: contactName,
      subject: '',
      content: '',
      duration: '',
      scheduledDate: '',
      scheduledTime: ''
    });
    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const shouldShowField = (field: string) => config.fields.includes(field);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={config.title} maxWidth="lg">
      <div className="flex items-center space-x-3 mb-6">
        <div className={`p-2 rounded-lg bg-${config.color}-100`}>
          <IconComponent className={`w-6 h-6 text-${config.color}-600`} />
        </div>
        <div>
          <h4 className="text-lg font-medium text-gray-900">{config.title}</h4>
          <p className="text-sm text-gray-500">
            {type === 'email' && 'Compose and send an email'}
            {type === 'call' && 'Log details of a phone call'}
            {type === 'text' && 'Send a text message'}
            {type === 'meeting' && 'Schedule a meeting or video call'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {shouldShowField('contact') && (
          <div>
            <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-1">
              Contact *
            </label>
            <input
              type="text"
              id="contact"
              name="contact"
              required
              value={formData.contact}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Contact name"
            />
          </div>
        )}

        {shouldShowField('subject') && (
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
              Subject *
            </label>
            <input
              type="text"
              id="subject"
              name="subject"
              required
              value={formData.subject}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={
                type === 'email' ? 'Email subject' :
                type === 'call' ? 'Call purpose' :
                type === 'meeting' ? 'Meeting title' : 'Subject'
              }
            />
          </div>
        )}

        {shouldShowField('scheduledDate') && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                id="scheduledDate"
                name="scheduledDate"
                required
                value={formData.scheduledDate}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700 mb-1">
                Time *
              </label>
              <input
                type="time"
                id="scheduledTime"
                name="scheduledTime"
                required
                value={formData.scheduledTime}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {shouldShowField('duration') && (
          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
              Duration (minutes)
            </label>
            <input
              type="number"
              id="duration"
              name="duration"
              value={formData.duration}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="30"
            />
          </div>
        )}

        {shouldShowField('content') && (
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              {type === 'email' ? 'Message' : 
               type === 'call' ? 'Call Notes' :
               type === 'text' ? 'Message' : 'Agenda'} *
            </label>
            <textarea
              id="content"
              name="content"
              rows={type === 'text' ? 3 : 5}
              required
              value={formData.content}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={
                type === 'email' ? 'Type your email message...' :
                type === 'call' ? 'Notes about the call...' :
                type === 'text' ? 'Type your message...' :
                'Meeting agenda and notes...'
              }
            />
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`px-4 py-2 text-sm font-medium text-white bg-${config.color}-600 border border-transparent rounded-md hover:bg-${config.color}-700 focus:outline-none focus:ring-2 focus:ring-${config.color}-500`}
          >
            {type === 'email' ? 'Send Email' :
             type === 'call' ? 'Log Call' :
             type === 'text' ? 'Send Message' :
             'Schedule Meeting'}
          </button>
        </div>
      </form>
    </Modal>
  );
}
