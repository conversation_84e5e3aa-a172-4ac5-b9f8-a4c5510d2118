(()=>{var A;function C(H){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},C(H)}function x(H,J){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(H);J&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(H,Z).enumerable})),X.push.apply(X,Y)}return X}function Q(H){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?x(Object(X),!0).forEach(function(Y){N(H,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):x(Object(X)).forEach(function(Y){Object.defineProperty(H,Y,Object.getOwnPropertyDescriptor(X,Y))})}return H}function N(H,J,X){if(J=z(J),J in H)Object.defineProperty(H,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[J]=X;return H}function z(H){var J=E(H,"string");return C(J)=="symbol"?J:String(J)}function E(H,J){if(C(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(H,J||"default");if(C(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(H)}var W=Object.defineProperty,XH=function H(J,X){for(var Y in X)W(J,Y,{get:X[Y],enumerable:!0,configurable:!0,set:function Z(B){return X[Y]=function(){return B}}})},D={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629",two:"\u062B\u0627\u0646\u064A\u062A\u064A\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u064A\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u064A\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u062D\u0648\u0627\u0644\u064A \u0633\u0627\u0639\u0629",two:"\u062D\u0648\u0627\u0644\u064A \u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0633\u0627\u0639\u0627\u062A",other:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0633\u0627\u0639\u0629"},xHours:{one:"\u0633\u0627\u0639\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645",two:"\u064A\u0648\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u062D\u0648\u0627\u0644\u064A \u0623\u0633\u0628\u0648\u0639",two:"\u062D\u0648\u0627\u0644\u064A \u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0623\u0633\u0628\u0648\u0639"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639"},aboutXMonths:{one:"\u062D\u0648\u0627\u0644\u064A \u0634\u0647\u0631",two:"\u062D\u0648\u0627\u0644\u064A \u0634\u0647\u0631\u064A\u0646",threeToTen:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0623\u0634\u0647\u0631",other:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0634\u0647\u0631"},xMonths:{one:"\u0634\u0647\u0631",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u062D\u0648\u0627\u0644\u064A \u0633\u0646\u0629",two:"\u062D\u0648\u0627\u0644\u064A \u0633\u0646\u062A\u064A\u0646",threeToTen:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0633\u0646\u064A\u0646",other:"\u062D\u0648\u0627\u0644\u064A {{count}} \u0633\u0646\u0629"},xYears:{one:"\u0639\u0627\u0645",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0633\u0646\u0629",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0633\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0633\u0646\u064A\u0646",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0633\u0646\u0629"},almostXYears:{one:"\u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u064B\u0627",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u064B\u0627",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u064B\u0627",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u064B\u0627"}},S=function H(J,X,Y){var Z,B=D[J];if(typeof B==="string")Z=B;else if(X===1)Z=B.one;else if(X===2)Z=B.two;else if(X<=10)Z=B.threeToTen.replace("{{count}}",String(X));else Z=B.other.replace("{{count}}",String(X));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"\u0641\u064A \u062E\u0644\u0627\u0644 ".concat(Z);else return"\u0645\u0646\u0630 ".concat(Z);return Z};function $(H){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):H.defaultWidth,Y=H.formats[X]||H.formats[H.defaultWidth];return Y}}var M={full:"EEEE\u060C do MMMM y",long:"do MMMM y",medium:"dd/MMM/y",short:"d/MM/y"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u0627\u0644\u0633\u0627\u0639\u0629' {{time}}",long:"{{date}} '\u0627\u0644\u0633\u0627\u0639\u0629' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee '\u0627\u0644\u0644\u064A \u062C\u0627\u064A \u0627\u0644\u0633\u0627\u0639\u0629' p",yesterday:"'\u0625\u0645\u0628\u0627\u0631\u062D \u0627\u0644\u0633\u0627\u0639\u0629' p",today:"'\u0627\u0644\u0646\u0647\u0627\u0631\u062F\u0629 \u0627\u0644\u0633\u0627\u0639\u0629' p",tomorrow:"'\u0628\u0643\u0631\u0629 \u0627\u0644\u0633\u0627\u0639\u0629' p",nextWeek:"eeee '\u0627\u0644\u0633\u0627\u0639\u0629' p",other:"P"},w=function H(J,X,Y,Z){return j[J]};function I(H){return function(J,X){var Y=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",Z;if(Y==="formatting"&&H.formattingValues){var B=H.defaultFormattingWidth||H.defaultWidth,G=X!==null&&X!==void 0&&X.width?String(X.width):B;Z=H.formattingValues[G]||H.formattingValues[B]}else{var T=H.defaultWidth,q=X!==null&&X!==void 0&&X.width?String(X.width):H.defaultWidth;Z=H.values[q]||H.values[T]}var U=H.argumentCallback?H.argumentCallback(J):J;return Z[U]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645","\u0628.\u0645"],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},f={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},F={narrow:["\u064A","\u0641","\u0645","\u0623","\u0645","\u064A","\u064A","\u0623","\u0633","\u0623","\u0646","\u062F"],abbreviated:["\u064A\u0646\u0627","\u0641\u0628\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u0640","\u064A\u0648\u0644\u0640","\u0623\u063A\u0633\u0640","\u0633\u0628\u062A\u0640","\u0623\u0643\u062A\u0640","\u0646\u0648\u0641\u0640","\u062F\u064A\u0633\u0640"],wide:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648","\u0623\u063A\u0633\u0637\u0633","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"]},v={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0627\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},P={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631\u0627\u064B",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631\u0627\u064B",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u064B",night:"\u0644\u064A\u0644\u0627\u064B"}},k={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631\u0627\u064B",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0638\u0647\u0631\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"}},b=function H(J,X){return String(J)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function H(J){return J-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.width,Z=Y&&H.matchPatterns[Y]||H.matchPatterns[H.defaultMatchWidth],B=J.match(Z);if(!B)return null;var G=B[0],T=Y&&H.parsePatterns[Y]||H.parsePatterns[H.defaultParseWidth],q=Array.isArray(T)?y(T,function(K){return K.test(G)}):m(T,function(K){return K.test(G)}),U;U=H.valueCallback?H.valueCallback(q):q,U=X.valueCallback?X.valueCallback(U):U;var JH=J.slice(G.length);return{value:U,rest:JH}}}function m(H,J){for(var X in H)if(Object.prototype.hasOwnProperty.call(H,X)&&J(H[X]))return X;return}function y(H,J){for(var X=0;X<H.length;X++)if(J(H[X]))return X;return}function c(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.match(H.matchPattern);if(!Y)return null;var Z=Y[0],B=J.match(H.parsePattern);if(!B)return null;var G=H.valueCallback?H.valueCallback(B[0]):B[0];G=X.valueCallback?X.valueCallback(G):G;var T=J.slice(Z.length);return{value:G,rest:T}}}var p=/^(\d+)/,d=/\d+/i,g={narrow:/^(ق|ب)/g,abbreviated:/^(ق.م|ب.م)/g,wide:/^(قبل الميلاد|بعد الميلاد)/g},u={any:[/^ق/g,/^ب/g]},l={narrow:/^[1234]/,abbreviated:/^ر[1234]/,wide:/^الربع (الأول|الثاني|الثالث|الرابع)/},i={wide:[/الربع الأول/,/الربع الثاني/,/الربع الثالث/,/الربع الرابع/],any:[/1/,/2/,/3/,/4/]},n={narrow:/^(ي|ف|م|أ|س|ن|د)/,abbreviated:/^(ينا|فبر|مارس|أبريل|مايو|يونـ|يولـ|أغسـ|سبتـ|أكتـ|نوفـ|ديسـ)/,wide:/^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/},s={narrow:[/^ي/,/^ف/,/^م/,/^أ/,/^م/,/^ي/,/^ي/,/^أ/,/^س/,/^أ/,/^ن/,/^د/],any:[/^ينا/,/^فبر/,/^مارس/,/^أبريل/,/^مايو/,/^يون/,/^يول/,/^أغس/,/^سبت/,/^أكت/,/^نوف/,/^ديس/]},o={narrow:/^(ح|ن|ث|ر|خ|ج|س)/,short:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/,abbreviated:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/,wide:/^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/},r={narrow:[/^ح/,/^ن/,/^ث/,/^ر/,/^خ/,/^ج/,/^س/],any:[/أحد/,/اثنين/,/ثلاثاء/,/أربعاء/,/خميس/,/جمعة/,/سبت/]},a={narrow:/^(ص|م|ن|ظ|في الصباح|بعد الظهر|في المساء|في الليل)/,abbreviated:/^(ص|م|نصف الليل|ظهراً|في الصباح|بعد الظهر|في المساء|في الليل)/,wide:/^(ص|م|نصف الليل|في الصباح|ظهراً|بعد الظهر|في المساء|في الليل)/,any:/^(ص|م|صباح|ظهر|مساء|ليل)/},e={any:{am:/^ص/,pm:/^م/,midnight:/^ن/,noon:/^ظ/,morning:/^ص/,afternoon:/^بعد/,evening:/^م/,night:/^ل/}},t={ordinalNumber:c({matchPattern:p,parsePattern:d,valueCallback:function H(J){return parseInt(J,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function H(J){return J+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},HH={code:"ar-EG",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{arEG:HH})})})();

//# debugId=F87B13F9F95FCD8964756E2164756E21
