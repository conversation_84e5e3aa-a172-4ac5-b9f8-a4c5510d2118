(()=>{var E;function B(G){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},B(G)}function N(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?N(Object(J),!0).forEach(function(X){W(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):N(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function W(G,H,J){if(H=z(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function z(G){var H=M(G,"string");return B(H)=="symbol"?H:String(H)}function M(G,H){if(B(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(B(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var S=Object.defineProperty,YG=function G(H,J){for(var X in J)S(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"bir saniy\u0259d\u0259n az",other:"{{count}} bir saniy\u0259d\u0259n az"},xSeconds:{one:"1 saniy\u0259",other:"{{count}} saniy\u0259"},halfAMinute:"yar\u0131m d\u0259qiq\u0259",lessThanXMinutes:{one:"bir d\u0259qiq\u0259d\u0259n az",other:"{{count}} bir d\u0259qiq\u0259d\u0259n az"},xMinutes:{one:"bir d\u0259qiq\u0259",other:"{{count}} d\u0259qiq\u0259"},aboutXHours:{one:"t\u0259xmin\u0259n 1 saat",other:"t\u0259xmin\u0259n {{count}} saat"},xHours:{one:"1 saat",other:"{{count}} saat"},xDays:{one:"1 g\xFCn",other:"{{count}} g\xFCn"},aboutXWeeks:{one:"t\u0259xmin\u0259n 1 h\u0259ft\u0259",other:"t\u0259xmin\u0259n {{count}} h\u0259ft\u0259"},xWeeks:{one:"1 h\u0259ft\u0259",other:"{{count}} h\u0259ft\u0259"},aboutXMonths:{one:"t\u0259xmin\u0259n 1 ay",other:"t\u0259xmin\u0259n {{count}} ay"},xMonths:{one:"1 ay",other:"{{count}} ay"},aboutXYears:{one:"t\u0259xmin\u0259n 1 il",other:"t\u0259xmin\u0259n {{count}} il"},xYears:{one:"1 il",other:"{{count}} il"},overXYears:{one:"1 ild\u0259n \xE7ox",other:"{{count}} ild\u0259n \xE7ox"},almostXYears:{one:"dem\u0259k olar ki 1 il",other:"dem\u0259k olar ki {{count}} il"}},R=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+" sonra";else return Y+" \u0259vv\u0259l";return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var x={full:"EEEE, do MMMM y 'il'",long:"do MMMM y 'il'",medium:"d MMM y 'il'",short:"dd.MM.yyyy"},L={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},V={full:"{{date}} {{time}} - 'd\u0259'",long:"{{date}} {{time}} - 'd\u0259'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},j={date:K({formats:x,defaultWidth:"full"}),time:K({formats:L,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},w={lastWeek:"'sonuncu' eeee p -'d\u0259'",yesterday:"'d\xFCn\u0259n' p -'d\u0259'",today:"'bug\xFCn' p -'d\u0259'",tomorrow:"'sabah' p -'d\u0259'",nextWeek:"eeee p -'d\u0259'",other:"P"},_=function G(H,J,X,Y){return w[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,$=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[$]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var v={narrow:["e.\u0259","b.e"],abbreviated:["e.\u0259","b.e"],wide:["eram\u0131zdan \u0259vv\u0259l","bizim era"]},P={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1ci kvartal","2ci kvartal","3c\xFC kvartal","4c\xFC kvartal"]},F={narrow:["Y","F","M","A","M","\u0130","\u0130","A","S","O","N","D"],abbreviated:["Yan","Fev","Mar","Apr","May","\u0130yun","\u0130yul","Avq","Sen","Okt","Noy","Dek"],wide:["Yanvar","Fevral","Mart","Aprel","May","\u0130yun","\u0130yul","Avqust","Sentyabr","Oktyabr","Noyabr","Dekabr"]},k={narrow:["B.","B.e","\xC7.a","\xC7.","C.a","C.","\u015E."],short:["B.","B.e","\xC7.a","\xC7.","C.a","C.","\u015E."],abbreviated:["Baz","Baz.e","\xC7\u0259r.a","\xC7\u0259r","C\xFCm.a","C\xFCm","\u015E\u0259"],wide:["Bazar","Bazar ert\u0259si","\xC7\u0259r\u015F\u0259nb\u0259 ax\u015Fam\u0131","\xC7\u0259r\u015F\u0259nb\u0259","C\xFCm\u0259 ax\u015Fam\u0131","C\xFCm\u0259","\u015E\u0259nb\u0259"]},f={narrow:{am:"am",pm:"pm",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},abbreviated:{am:"AM",pm:"PM",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},wide:{am:"a.m.",pm:"p.m.",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"}},h={narrow:{am:"a",pm:"p",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},abbreviated:{am:"AM",pm:"PM",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},wide:{am:"a.m.",pm:"p.m.",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"}},O={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-\xFCnc\xFC",4:"-\xFCnc\xFC",100:"-\xFCnc\xFC",6:"-nc\u0131",9:"-uncu",10:"-uncu",30:"-uncu",60:"-\u0131nc\u0131",90:"-\u0131nc\u0131"},b=function G(H){if(H===0)return H+"-\u0131nc\u0131";var J=H%10,X=H%100-J,Y=H>=100?100:null;if(O[J])return O[J];else if(O[X])return O[X];else if(Y!==null)return O[Y];return""},y=function G(H,J){var X=Number(H),Y=b(X);return X+Y},c={ordinalNumber:y,era:I({values:v,defaultWidth:"wide"}),quarter:I({values:P,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:k,defaultWidth:"wide"}),dayPeriod:I({values:f,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function Q(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],$=Array.isArray(T)?p(T,function(A){return A.test(C)}):m(T,function(A){return A.test(C)}),U;U=G.valueCallback?G.valueCallback($):$,U=J.valueCallback?J.valueCallback(U):U;var XG=H.slice(C.length);return{value:U,rest:XG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function p(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function d(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var g=/^(\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i,l=/\d+/i,u={narrow:/^(b|a)$/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)$/i,wide:/^(bizim eradan əvvəl|bizim era)$/i},i={any:[/^b$/i,/^(a|c)$/i]},n={narrow:/^[1234]$/i,abbreviated:/^K[1234]$/i,wide:/^[1234](ci)? kvartal$/i},s={any:[/1/i,/2/i,/3/i,/4/i]},o={narrow:/^[(?-i)yfmaisond]$/i,abbreviated:/^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,wide:/^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i},r={narrow:[/^[(?-i)y]$/i,/^[(?-i)f]$/i,/^[(?-i)m]$/i,/^[(?-i)a]$/i,/^[(?-i)m]$/i,/^[(?-i)i]$/i,/^[(?-i)i]$/i,/^[(?-i)a]$/i,/^[(?-i)s]$/i,/^[(?-i)o]$/i,/^[(?-i)n]$/i,/^[(?-i)d]$/i],abbreviated:[/^Yan$/i,/^Fev$/i,/^Mar$/i,/^Apr$/i,/^May$/i,/^İyun$/i,/^İyul$/i,/^Avg$/i,/^Sen$/i,/^Okt$/i,/^Noy$/i,/^Dek$/i],wide:[/^Yanvar$/i,/^Fevral$/i,/^Mart$/i,/^Aprel$/i,/^May$/i,/^İyun$/i,/^İyul$/i,/^Avgust$/i,/^Sentyabr$/i,/^Oktyabr$/i,/^Noyabr$/i,/^Dekabr$/i]},a={narrow:/^(B\.|B\.e|Ç\.a|Ç\.|C\.a|C\.|Ş\.)$/i,short:/^(B\.|B\.e|Ç\.a|Ç\.|C\.a|C\.|Ş\.)$/i,abbreviated:/^(Baz\.e|Çər|Çər\.a|Cüm|Cüm\.a|Şə)$/i,wide:/^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i},e={narrow:[/^B\.$/i,/^B\.e$/i,/^Ç\.a$/i,/^Ç\.$/i,/^C\.a$/i,/^C\.$/i,/^Ş\.$/i],abbreviated:[/^Baz$/i,/^Baz\.e$/i,/^Çər\.a$/i,/^Çər$/i,/^Cüm\.a$/i,/^Cüm$/i,/^Şə$/i],wide:[/^Bazar$/i,/^Bazar ertəsi$/i,/^Çərşənbə axşamı$/i,/^Çərşənbə$/i,/^Cümə axşamı$/i,/^Cümə$/i,/^Şənbə$/i],any:[/^B\.$/i,/^B\.e$/i,/^Ç\.a$/i,/^Ç\.$/i,/^C\.a$/i,/^C\.$/i,/^Ş\.$/i]},t={narrow:/^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,any:/^(am|pm|a\.m\.|p\.m\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i},GG={any:{am:/^a$/i,pm:/^p$/i,midnight:/^gecəyarı$/i,noon:/^gün$/i,morning:/səhər$/i,afternoon:/gündüz$/i,evening:/axşam$/i,night:/gecə$/i}},HG={ordinalNumber:d({matchPattern:g,parsePattern:l,valueCallback:function G(H){return parseInt(H,10)}}),era:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),quarter:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"narrow"}),day:Q({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:t,defaultMatchWidth:"any",parsePatterns:GG,defaultParseWidth:"any"})},JG={code:"az",formatDistance:R,formatLong:j,formatRelative:_,localize:c,match:HG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{az:JG})})})();

//# debugId=32216761BDC32DF164756E2164756E21
