'use client';

import { 
  HomeIcon, 
  UserGroupIcon, 
  UsersIcon, 
  ChatBubbleLeftRightIcon, 
  ChartBarIcon 
} from '@heroicons/react/24/outline';

interface SidebarProps {
  currentPage: string;
  setCurrentPage: (page: string) => void;
  onNavigate?: () => void;
}

const navigation = [
  { name: 'Dashboard', href: 'dashboard', icon: HomeIcon },
  { name: 'Leads', href: 'leads', icon: UserGroupIcon },
  { name: 'Contacts', href: 'contacts', icon: UsersIcon },
  { name: 'Communications', href: 'communications', icon: ChatBubbleLeftRightIcon },
  { name: 'Analytics', href: 'analytics', icon: ChartBarIcon },
];

export default function Sidebar({ currentPage, setCurrentPage, onNavigate }: SidebarProps) {
  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      <div className="flex items-center justify-center h-16 px-4 bg-blue-600">
        <h1 className="text-xl font-bold text-white">Sales Dashboard</h1>
      </div>
      
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = currentPage === item.href;
          return (
            <button
              key={item.name}
              onClick={() => {
                setCurrentPage(item.href);
                onNavigate?.();
              }}
              className={`
                w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors
                ${isActive
                  ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }
              `}
            >
              <item.icon className="w-5 h-5 mr-3" />
              {item.name}
            </button>
          );
        })}
      </nav>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">U</span>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">User</p>
            <p className="text-xs text-gray-500">Sales Manager</p>
          </div>
        </div>
      </div>
    </div>
  );
}
