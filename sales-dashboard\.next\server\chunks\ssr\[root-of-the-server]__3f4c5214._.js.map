{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { \n  HomeIcon, \n  UserGroupIcon, \n  UsersIcon, \n  ChatBubbleLeftRightIcon, \n  ChartBarIcon \n} from '@heroicons/react/24/outline';\n\ninterface SidebarProps {\n  currentPage: string;\n  setCurrentPage: (page: string) => void;\n  onNavigate?: () => void;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: 'dashboard', icon: HomeIcon },\n  { name: 'Leads', href: 'leads', icon: UserGroupIcon },\n  { name: 'Contacts', href: 'contacts', icon: UsersIcon },\n  { name: 'Communications', href: 'communications', icon: ChatBubbleLeftRightIcon },\n  { name: 'Analytics', href: 'analytics', icon: ChartBarIcon },\n];\n\nexport default function Sidebar({ currentPage, setCurrentPage, onNavigate }: SidebarProps) {\n  return (\n    <div className=\"flex flex-col w-64 bg-white shadow-lg\">\n      <div className=\"flex items-center justify-center h-16 px-4 bg-blue-600\">\n        <h1 className=\"text-xl font-bold text-white\">Sales Dashboard</h1>\n      </div>\n      \n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {navigation.map((item) => {\n          const isActive = currentPage === item.href;\n          return (\n            <button\n              key={item.name}\n              onClick={() => {\n                setCurrentPage(item.href);\n                onNavigate?.();\n              }}\n              className={`\n                w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\n                ${isActive\n                  ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\n                }\n              `}\n            >\n              <item.icon className=\"w-5 h-5 mr-3\" />\n              {item.name}\n            </button>\n          );\n        })}\n      </nav>\n      \n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex items-center\">\n          <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-sm font-medium\">U</span>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium text-gray-700\">User</p>\n            <p className=\"text-xs text-gray-500\">Sales Manager</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACvD;QAAE,MAAM;QAAS,MAAM;QAAS,MAAM,yNAAA,CAAA,gBAAa;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,iNAAA,CAAA,YAAS;IAAC;IACtD;QAAE,MAAM;QAAkB,MAAM;QAAkB,MAAM,6OAAA,CAAA,0BAAuB;IAAC;IAChF;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,uNAAA,CAAA,eAAY;IAAC;CAC5D;AAEc,SAAS,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAgB;IACvF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAA+B;;;;;;;;;;;0BAG/C,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,gBAAgB,KAAK,IAAI;oBAC1C,qBACE,8OAAC;wBAEC,SAAS;4BACP,eAAe,KAAK,IAAI;4BACxB;wBACF;wBACA,WAAW,CAAC;;gBAEV,EAAE,WACE,yDACA,sDACH;cACH,CAAC;;0CAED,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,IAAI;;uBAdL,KAAK,IAAI;;;;;gBAiBpB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/MobileNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { Bars3Icon } from '@heroicons/react/24/outline';\n\ninterface MobileNavigationProps {\n  currentPage: string;\n  onMenuClick: () => void;\n}\n\nconst pageNames = {\n  dashboard: 'Dashboard',\n  leads: 'Leads',\n  contacts: 'Contacts',\n  communications: 'Communications',\n  analytics: 'Analytics',\n};\n\nexport default function MobileNavigation({ currentPage, onMenuClick }: MobileNavigationProps) {\n  return (\n    <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 py-3\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={onMenuClick}\n            className=\"p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors\"\n          >\n            <Bars3Icon className=\"w-6 h-6\" />\n          </button>\n          <h1 className=\"text-lg font-semibold text-gray-900\">\n            {pageNames[currentPage as keyof typeof pageNames] || 'Dashboard'}\n          </h1>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-sm font-medium\">U</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,MAAM,YAAY;IAChB,WAAW;IACX,OAAO;IACP,UAAU;IACV,gBAAgB;IAChB,WAAW;AACb;AAEe,SAAS,iBAAiB,EAAE,WAAW,EAAE,WAAW,EAAyB;IAC1F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;4BAAG,WAAU;sCACX,SAAS,CAAC,YAAsC,IAAI;;;;;;;;;;;;8BAGzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/DashboardOverview.tsx"], "sourcesContent": ["'use client';\n\nimport { \n  ArrowUpIcon, \n  ArrowDownIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ChatBubbleLeftIcon,\n  CurrencyDollarIcon\n} from '@heroicons/react/24/outline';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';\n\n// Sample data\nconst salesData = [\n  { month: 'Jan', sales: 4000, leads: 240 },\n  { month: 'Feb', sales: 3000, leads: 139 },\n  { month: 'Mar', sales: 2000, leads: 980 },\n  { month: 'Apr', sales: 2780, leads: 390 },\n  { month: 'May', sales: 1890, leads: 480 },\n  { month: 'Jun', sales: 2390, leads: 380 },\n];\n\nconst leadSourceData = [\n  { name: 'Website', value: 400, color: '#0088FE' },\n  { name: 'Social Media', value: 300, color: '#00C49F' },\n  { name: '<PERSON><PERSON>', value: 300, color: '#FFBB28' },\n  { name: 'Referral', value: 200, color: '#FF8042' },\n];\n\nconst communicationData = [\n  { type: 'Email', count: 45 },\n  { type: 'Phone', count: 23 },\n  { type: 'Text', count: 12 },\n  { type: 'Meeting', count: 8 },\n];\n\nconst stats = [\n  { name: 'Total Revenue', value: '$45,231', change: '+20.1%', changeType: 'positive', icon: CurrencyDollarIcon },\n  { name: 'Active Leads', value: '1,234', change: '+15.3%', changeType: 'positive', icon: ArrowUpIcon },\n  { name: 'Calls Made', value: '89', change: '-2.4%', changeType: 'negative', icon: PhoneIcon },\n  { name: 'Emails Sent', value: '156', change: '****%', changeType: 'positive', icon: EnvelopeIcon },\n];\n\nexport default function DashboardOverview() {\n  return (\n    <div className=\"p-4 lg:p-6 space-y-4 lg:space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0\">\n        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900\">Dashboard Overview</h1>\n        <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3\">\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\">\n            Add Lead\n          </button>\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\">\n            New Contact\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n        {stats.map((stat) => (\n          <div key={stat.name} className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <stat.icon className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">{stat.name}</dt>\n                  <dd className=\"flex items-baseline\">\n                    <div className=\"text-2xl font-semibold text-gray-900\">{stat.value}</div>\n                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.changeType === 'positive' ? (\n                        <ArrowUpIcon className=\"self-center flex-shrink-0 h-4 w-4\" />\n                      ) : (\n                        <ArrowDownIcon className=\"self-center flex-shrink-0 h-4 w-4\" />\n                      )}\n                      <span className=\"sr-only\">{stat.changeType === 'positive' ? 'Increased' : 'Decreased'} by</span>\n                      {stat.change}\n                    </div>\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Sales Trend Chart */}\n        <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Sales & Leads Trend</h3>\n          <ResponsiveContainer width=\"100%\" height={250}>\n            <LineChart data={salesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"month\" />\n              <YAxis />\n              <Tooltip />\n              <Line type=\"monotone\" dataKey=\"sales\" stroke=\"#3B82F6\" strokeWidth={2} />\n              <Line type=\"monotone\" dataKey=\"leads\" stroke=\"#10B981\" strokeWidth={2} />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Lead Sources Pie Chart */}\n        <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Lead Sources</h3>\n          <ResponsiveContainer width=\"100%\" height={250}>\n            <PieChart>\n              <Pie\n                data={leadSourceData}\n                cx=\"50%\"\n                cy=\"50%\"\n                labelLine={false}\n                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                outerRadius={80}\n                fill=\"#8884d8\"\n                dataKey=\"value\"\n              >\n                {leadSourceData.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.color} />\n                ))}\n              </Pie>\n              <Tooltip />\n            </PieChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Communication Activity */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Communication Activity (This Week)</h3>\n        <ResponsiveContainer width=\"100%\" height={250}>\n          <BarChart data={communicationData}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"type\" />\n            <YAxis />\n            <Tooltip />\n            <Bar dataKey=\"count\" fill=\"#3B82F6\" />\n          </BarChart>\n        </ResponsiveContainer>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h3>\n        <div className=\"space-y-4\">\n          {[\n            { action: 'New lead added', contact: 'John Smith', time: '2 hours ago', type: 'lead' },\n            { action: 'Email sent', contact: 'Sarah Johnson', time: '4 hours ago', type: 'email' },\n            { action: 'Call completed', contact: 'Mike Davis', time: '6 hours ago', type: 'call' },\n            { action: 'Meeting scheduled', contact: 'Lisa Wilson', time: '1 day ago', type: 'meeting' },\n          ].map((activity, index) => (\n            <div key={index} className=\"flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg\">\n              <div className={`w-2 h-2 rounded-full ${\n                activity.type === 'lead' ? 'bg-green-500' :\n                activity.type === 'email' ? 'bg-blue-500' :\n                activity.type === 'call' ? 'bg-yellow-500' : 'bg-purple-500'\n              }`} />\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900\">{activity.action}</p>\n                <p className=\"text-sm text-gray-500\">{activity.contact}</p>\n              </div>\n              <span className=\"text-xs text-gray-400\">{activity.time}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;AAYA,cAAc;AACd,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,OAAO;IAAI;CACzC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAW,OAAO;QAAK,OAAO;IAAU;IAChD;QAAE,MAAM;QAAgB,OAAO;QAAK,OAAO;IAAU;IACrD;QAAE,MAAM;QAAS,OAAO;QAAK,OAAO;IAAU;IAC9C;QAAE,MAAM;QAAY,OAAO;QAAK,OAAO;IAAU;CAClD;AAED,MAAM,oBAAoB;IACxB;QAAE,MAAM;QAAS,OAAO;IAAG;IAC3B;QAAE,MAAM;QAAS,OAAO;IAAG;IAC3B;QAAE,MAAM;QAAQ,OAAO;IAAG;IAC1B;QAAE,MAAM;QAAW,OAAO;IAAE;CAC7B;AAED,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAiB,OAAO;QAAW,QAAQ;QAAU,YAAY;QAAY,MAAM,mOAAA,CAAA,qBAAkB;IAAC;IAC9G;QAAE,MAAM;QAAgB,OAAO;QAAS,QAAQ;QAAU,YAAY;QAAY,MAAM,qNAAA,CAAA,cAAW;IAAC;IACpG;QAAE,MAAM;QAAc,OAAO;QAAM,QAAQ;QAAS,YAAY;QAAY,MAAM,iNAAA,CAAA,YAAS;IAAC;IAC5F;QAAE,MAAM;QAAe,OAAO;QAAO,QAAQ;QAAS,YAAY;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;CAClG;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAA0F;;;;;;0CAG5G,8OAAC;gCAAO,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;0BAOlH,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAoB,WAAU;kCAC7B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8C,KAAK,IAAI;;;;;;0DACrE,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEAAwC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAI,WAAW,CAAC,+CAA+C,EAC9D,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;;4DACC,KAAK,UAAU,KAAK,2BACnB,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAEvB,8OAAC,yNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EAE3B,8OAAC;gEAAK,WAAU;;oEAAW,KAAK,UAAU,KAAK,aAAa,cAAc;oEAAY;;;;;;;4DACrF,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnBd,KAAK,IAAI;;;;;;;;;;0BA8BvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,oJAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,SAAQ;4CAAQ,QAAO;4CAAU,aAAa;;;;;;sDACpE,8OAAC,oJAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,SAAQ;4CAAQ,QAAO;4CAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAM1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;sDACP,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,WAAW;4CACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;4CACtE,aAAa;4CACb,MAAK;4CACL,SAAQ;sDAEP,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,KAAK;mDAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAO,QAAQ;kCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;4BAAC,MAAM;;8CACd,8OAAC,6JAAA,CAAA,gBAAa;oCAAC,iBAAgB;;;;;;8CAC/B,8OAAC,qJAAA,CAAA,QAAK;oCAAC,SAAQ;;;;;;8CACf,8OAAC,qJAAA,CAAA,QAAK;;;;;8CACN,8OAAC,uJAAA,CAAA,UAAO;;;;;8CACR,8OAAC,mJAAA,CAAA,MAAG;oCAAC,SAAQ;oCAAQ,MAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,QAAQ;gCAAkB,SAAS;gCAAc,MAAM;gCAAe,MAAM;4BAAO;4BACrF;gCAAE,QAAQ;gCAAc,SAAS;gCAAiB,MAAM;gCAAe,MAAM;4BAAQ;4BACrF;gCAAE,QAAQ;gCAAkB,SAAS;gCAAc,MAAM;gCAAe,MAAM;4BAAO;4BACrF;gCAAE,QAAQ;gCAAqB,SAAS;gCAAe,MAAM;gCAAa,MAAM;4BAAU;yBAC3F,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EACpC,SAAS,IAAI,KAAK,SAAS,iBAC3B,SAAS,IAAI,KAAK,UAAU,gBAC5B,SAAS,IAAI,KAAK,SAAS,kBAAkB,iBAC7C;;;;;;kDACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqC,SAAS,MAAM;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAyB,SAAS,OAAO;;;;;;;;;;;;kDAExD,8OAAC;wCAAK,WAAU;kDAAyB,SAAS,IAAI;;;;;;;+BAV9C;;;;;;;;;;;;;;;;;;;;;;AAiBtB", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/LeadsPage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  PlusIcon, \n  MagnifyingGlassIcon,\n  FunnelIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ChatBubbleLeftIcon\n} from '@heroicons/react/24/outline';\n\ninterface Lead {\n  id: number;\n  name: string;\n  email: string;\n  phone: string;\n  company: string;\n  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'closed';\n  score: number;\n  source: string;\n  value: number;\n  lastContact: string;\n}\n\nconst sampleLeads: Lead[] = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Tech Corp',\n    status: 'new',\n    score: 85,\n    source: 'Website',\n    value: 15000,\n    lastContact: '2024-01-15'\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Innovation Inc',\n    status: 'contacted',\n    score: 72,\n    source: 'Social Media',\n    value: 25000,\n    lastContact: '2024-01-14'\n  },\n  {\n    id: 3,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Business Solutions',\n    status: 'qualified',\n    score: 91,\n    source: 'Referral',\n    value: 35000,\n    lastContact: '2024-01-13'\n  },\n];\n\nconst statusColors = {\n  new: 'bg-blue-100 text-blue-800',\n  contacted: 'bg-yellow-100 text-yellow-800',\n  qualified: 'bg-green-100 text-green-800',\n  proposal: 'bg-purple-100 text-purple-800',\n  closed: 'bg-gray-100 text-gray-800',\n};\n\nexport default function LeadsPage() {\n  const [leads, setLeads] = useState<Lead[]>(sampleLeads);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const filteredLeads = leads.filter(lead => {\n    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         lead.company.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         lead.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  const getScoreColor = (score: number) => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  return (\n    <div className=\"p-4 lg:p-6 space-y-4 lg:space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0\">\n        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900\">Leads Management</h1>\n        <button\n          onClick={() => setShowAddModal(true)}\n          className=\"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n        >\n          <PlusIcon className=\"w-5 h-5 mr-2\" />\n          Add Lead\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"relative flex-1\">\n          <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search leads...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <FunnelIcon className=\"h-5 w-5 text-gray-400\" />\n          <select\n            value={statusFilter}\n            onChange={(e) => setStatusFilter(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Status</option>\n            <option value=\"new\">New</option>\n            <option value=\"contacted\">Contacted</option>\n            <option value=\"qualified\">Qualified</option>\n            <option value=\"proposal\">Proposal</option>\n            <option value=\"closed\">Closed</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-blue-600\">{leads.length}</div>\n          <div className=\"text-sm text-gray-500\">Total Leads</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {leads.filter(l => l.status === 'qualified').length}\n          </div>\n          <div className=\"text-sm text-gray-500\">Qualified</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-yellow-600\">\n            {leads.filter(l => l.status === 'contacted').length}\n          </div>\n          <div className=\"text-sm text-gray-500\">In Progress</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-purple-600\">\n            ${leads.reduce((sum, lead) => sum + lead.value, 0).toLocaleString()}\n          </div>\n          <div className=\"text-sm text-gray-500\">Total Value</div>\n        </div>\n      </div>\n\n      {/* Desktop Table View */}\n      <div className=\"hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Lead\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Contact Info\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Score\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Value\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredLeads.map((lead) => (\n                <tr key={lead.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{lead.name}</div>\n                      <div className=\"text-sm text-gray-500\">{lead.company}</div>\n                      <div className=\"text-xs text-gray-400\">{lead.source}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">{lead.email}</div>\n                    <div className=\"text-sm text-gray-500\">{lead.phone}</div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[lead.status]}`}>\n                      {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className={`text-sm font-medium ${getScoreColor(lead.score)}`}>\n                      {lead.score}/100\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    ${lead.value.toLocaleString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900\">\n                        <PhoneIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-900\">\n                        <EnvelopeIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-purple-600 hover:text-purple-900\">\n                        <ChatBubbleLeftIcon className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Mobile Card View */}\n      <div className=\"lg:hidden space-y-4\">\n        {filteredLeads.map((lead) => (\n          <div key={lead.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900\">{lead.name}</h3>\n                <p className=\"text-sm text-gray-500\">{lead.company}</p>\n                <p className=\"text-xs text-gray-400\">{lead.source}</p>\n              </div>\n              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[lead.status]}`}>\n                {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}\n              </span>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 mb-4\">\n              <div>\n                <p className=\"text-xs text-gray-500\">Email</p>\n                <p className=\"text-sm text-gray-900 truncate\">{lead.email}</p>\n              </div>\n              <div>\n                <p className=\"text-xs text-gray-500\">Phone</p>\n                <p className=\"text-sm text-gray-900\">{lead.phone}</p>\n              </div>\n              <div>\n                <p className=\"text-xs text-gray-500\">Score</p>\n                <p className={`text-sm font-medium ${getScoreColor(lead.score)}`}>\n                  {lead.score}/100\n                </p>\n              </div>\n              <div>\n                <p className=\"text-xs text-gray-500\">Value</p>\n                <p className=\"text-sm text-gray-900\">${lead.value.toLocaleString()}</p>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center pt-3 border-t border-gray-200\">\n              <span className=\"text-xs text-gray-500\">Last contact: {lead.lastContact}</span>\n              <div className=\"flex space-x-3\">\n                <button className=\"text-blue-600 hover:text-blue-900\">\n                  <PhoneIcon className=\"w-4 h-4\" />\n                </button>\n                <button className=\"text-green-600 hover:text-green-900\">\n                  <EnvelopeIcon className=\"w-4 h-4\" />\n                </button>\n                <button className=\"text-purple-600 hover:text-purple-900\">\n                  <ChatBubbleLeftIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAyBA,MAAM,cAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB,KAAK;IACL,WAAW;IACX,WAAW;IACX,UAAU;IACV,QAAQ;AACV;AAEe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC7E,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,MAAM,KAAK;QAChE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC,MAAM,MAAM;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;0CAErD,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;0CAErD,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAqC;oCAChD,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE,GAAG,cAAc;;;;;;;0CAEnE,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAK3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,8OAAC;gCAAM,WAAU;0CACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAqC,KAAK,IAAI;;;;;;sEAC7D,8OAAC;4DAAI,WAAU;sEAAyB,KAAK,OAAO;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAyB,KAAK,MAAM;;;;;;;;;;;;;;;;;0DAGvD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,KAAK;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,KAAK;;;;;;;;;;;;0DAEpD,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,CAAC,KAAK,MAAM,CAAC,EAAE;8DACrG,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;0DAG7D,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAW,CAAC,oBAAoB,EAAE,cAAc,KAAK,KAAK,GAAG;;wDAC/D,KAAK,KAAK;wDAAC;;;;;;;;;;;;0DAGhB,8OAAC;gDAAG,WAAU;;oDAAoD;oDAC9D,KAAK,KAAK,CAAC,cAAc;;;;;;;0DAE7B,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;sEAEvB,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlC7B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8C1B,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC,KAAK,IAAI;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,OAAO;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,MAAM;;;;;;;;;;;;kDAEnD,8OAAC;wCAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,CAAC,KAAK,MAAM,CAAC,EAAE;kDACrG,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAkC,KAAK,KAAK;;;;;;;;;;;;kDAE3D,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK;;;;;;;;;;;;kDAElD,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAW,CAAC,oBAAoB,EAAE,cAAc,KAAK,KAAK,GAAG;;oDAC7D,KAAK,KAAK;oDAAC;;;;;;;;;;;;;kDAGhB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;;oDAAwB;oDAAE,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;;0CAIpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAe,KAAK,WAAW;;;;;;;kDACvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBA3C5B,KAAK,EAAE;;;;;;;;;;;;;;;;AAoD3B", "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/ContactsPage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  PlusIcon, \n  MagnifyingGlassIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  BuildingOfficeIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline';\n\ninterface Contact {\n  id: number;\n  name: string;\n  email: string;\n  phone: string;\n  company: string;\n  position: string;\n  status: 'active' | 'inactive' | 'prospect';\n  lastContact: string;\n  totalValue: number;\n  notes: string;\n}\n\nconst sampleContacts: Contact[] = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Tech Corp',\n    position: 'CTO',\n    status: 'active',\n    lastContact: '2024-01-15',\n    totalValue: 45000,\n    notes: 'Interested in enterprise solution'\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Innovation Inc',\n    position: 'VP Sales',\n    status: 'prospect',\n    lastContact: '2024-01-12',\n    totalValue: 0,\n    notes: 'Potential partnership opportunity'\n  },\n  {\n    id: 3,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    company: 'Business Solutions',\n    position: 'CEO',\n    status: 'active',\n    lastContact: '2024-01-10',\n    totalValue: 75000,\n    notes: 'Long-term client, very satisfied'\n  },\n];\n\nconst statusColors = {\n  active: 'bg-green-100 text-green-800',\n  inactive: 'bg-gray-100 text-gray-800',\n  prospect: 'bg-blue-100 text-blue-800',\n};\n\nexport default function ContactsPage() {\n  const [contacts, setContacts] = useState<Contact[]>(sampleContacts);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);\n\n  const filteredContacts = contacts.filter(contact => {\n    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         contact.company.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         contact.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || contact.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div className=\"p-4 lg:p-6 space-y-4 lg:space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0\">\n        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900\">Contacts Management</h1>\n        <button className=\"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\">\n          <PlusIcon className=\"w-5 h-5 mr-2\" />\n          Add Contact\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"relative flex-1\">\n          <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search contacts...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value)}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Status</option>\n          <option value=\"active\">Active</option>\n          <option value=\"prospect\">Prospect</option>\n          <option value=\"inactive\">Inactive</option>\n        </select>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-blue-600\">{contacts.length}</div>\n          <div className=\"text-sm text-gray-500\">Total Contacts</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {contacts.filter(c => c.status === 'active').length}\n          </div>\n          <div className=\"text-sm text-gray-500\">Active</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-blue-600\">\n            {contacts.filter(c => c.status === 'prospect').length}\n          </div>\n          <div className=\"text-sm text-gray-500\">Prospects</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"text-2xl font-bold text-purple-600\">\n            ${contacts.reduce((sum, contact) => sum + contact.totalValue, 0).toLocaleString()}\n          </div>\n          <div className=\"text-sm text-gray-500\">Total Value</div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6\">\n        {/* Contacts List */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Contacts</h3>\n            </div>\n            <div className=\"divide-y divide-gray-200\">\n              {filteredContacts.map((contact) => (\n                <div \n                  key={contact.id} \n                  className={`p-4 lg:p-6 hover:bg-gray-50 cursor-pointer transition-colors ${\n                    selectedContact?.id === contact.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                  }`}\n                  onClick={() => setSelectedContact(contact)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\">\n                        <span className=\"text-white font-medium text-lg\">\n                          {contact.name.split(' ').map(n => n[0]).join('')}\n                        </span>\n                      </div>\n                      <div>\n                        <h4 className=\"text-lg font-medium text-gray-900\">{contact.name}</h4>\n                        <p className=\"text-sm text-gray-500\">{contact.position} at {contact.company}</p>\n                        <div className=\"flex items-center space-x-4 mt-1\">\n                          <span className=\"flex items-center text-sm text-gray-500\">\n                            <EnvelopeIcon className=\"w-4 h-4 mr-1\" />\n                            {contact.email}\n                          </span>\n                          <span className=\"flex items-center text-sm text-gray-500\">\n                            <PhoneIcon className=\"w-4 h-4 mr-1\" />\n                            {contact.phone}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[contact.status]}`}>\n                        {contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}\n                      </span>\n                      <p className=\"text-sm text-gray-500 mt-1\">\n                        ${contact.totalValue.toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Contact Details */}\n        <div className=\"lg:col-span-1\">\n          {selectedContact ? (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Contact Details</h3>\n              </div>\n              <div className=\"p-6 space-y-6\">\n                <div className=\"text-center\">\n                  <div className=\"w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto\">\n                    <span className=\"text-white font-medium text-2xl\">\n                      {selectedContact.name.split(' ').map(n => n[0]).join('')}\n                    </span>\n                  </div>\n                  <h4 className=\"text-xl font-medium text-gray-900 mt-3\">{selectedContact.name}</h4>\n                  <p className=\"text-gray-500\">{selectedContact.position}</p>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <BuildingOfficeIcon className=\"w-5 h-5 text-gray-400\" />\n                    <span className=\"text-sm text-gray-900\">{selectedContact.company}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <EnvelopeIcon className=\"w-5 h-5 text-gray-400\" />\n                    <span className=\"text-sm text-gray-900\">{selectedContact.email}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <PhoneIcon className=\"w-5 h-5 text-gray-400\" />\n                    <span className=\"text-sm text-gray-900\">{selectedContact.phone}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <CalendarIcon className=\"w-5 h-5 text-gray-400\" />\n                    <span className=\"text-sm text-gray-900\">Last contact: {selectedContact.lastContact}</span>\n                  </div>\n                </div>\n\n                <div>\n                  <h5 className=\"text-sm font-medium text-gray-900 mb-2\">Notes</h5>\n                  <p className=\"text-sm text-gray-600\">{selectedContact.notes}</p>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <button className=\"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                    <PhoneIcon className=\"w-4 h-4 mr-2\" />\n                    Call\n                  </button>\n                  <button className=\"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                    <EnvelopeIcon className=\"w-4 h-4 mr-2\" />\n                    Email\n                  </button>\n                  <button className=\"w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n                    <CalendarIcon className=\"w-4 h-4 mr-2\" />\n                    Schedule Meeting\n                  </button>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"text-center text-gray-500\">\n                <BuildingOfficeIcon className=\"w-12 h-12 mx-auto mb-4 text-gray-300\" />\n                <p>Select a contact to view details</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAyBA,MAAM,iBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB,QAAQ;IACR,UAAU;IACV,UAAU;AACZ;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QACnE,OAAO,iBAAiB;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,8OAAC;gCAAO,OAAM;0CAAW;;;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC,SAAS,MAAM;;;;;;0CAClE,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;0CAErD,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;0CAEvD,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAqC;oCAChD,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,UAAU,EAAE,GAAG,cAAc;;;;;;;0CAEjF,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;4CAEC,WAAW,CAAC,6DAA6D,EACvE,iBAAiB,OAAO,QAAQ,EAAE,GAAG,0CAA0C,IAC/E;4CACF,SAAS,IAAM,mBAAmB;sDAElC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;0EAGjD,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAqC,QAAQ,IAAI;;;;;;kFAC/D,8OAAC;wEAAE,WAAU;;4EAAyB,QAAQ,QAAQ;4EAAC;4EAAK,QAAQ,OAAO;;;;;;;kFAC3E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;;kGACd,8OAAC,uNAAA,CAAA,eAAY;wFAAC,WAAU;;;;;;oFACvB,QAAQ,KAAK;;;;;;;0FAEhB,8OAAC;gFAAK,WAAU;;kGACd,8OAAC,iNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACpB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,CAAC,QAAQ,MAAM,CAAC,EAAE;0EACxG,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC;;;;;;0EAEjE,8OAAC;gEAAE,WAAU;;oEAA6B;oEACtC,QAAQ,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;;2CAjCpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCA4CzB,8OAAC;wBAAI,WAAU;kCACZ,gCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;8DAGzD,8OAAC;oDAAG,WAAU;8DAA0C,gBAAgB,IAAI;;;;;;8DAC5E,8OAAC;oDAAE,WAAU;8DAAiB,gBAAgB,QAAQ;;;;;;;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,mOAAA,CAAA,qBAAkB;4DAAC,WAAU;;;;;;sEAC9B,8OAAC;4DAAK,WAAU;sEAAyB,gBAAgB,OAAO;;;;;;;;;;;;8DAElE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAK,WAAU;sEAAyB,gBAAgB,KAAK;;;;;;;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAAyB,gBAAgB,KAAK;;;;;;;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAK,WAAU;;gEAAwB;gEAAe,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;;sDAItF,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAyB,gBAAgB,KAAK;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGxC,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG3C,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;iDAOjD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/CommunicationsPage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  PlusIcon, \n  PhoneIcon,\n  EnvelopeIcon,\n  ChatBubbleLeftIcon,\n  VideoCameraIcon,\n  ClockIcon,\n  UserIcon\n} from '@heroicons/react/24/outline';\n\ninterface Communication {\n  id: number;\n  type: 'email' | 'call' | 'text' | 'meeting';\n  contact: string;\n  subject: string;\n  content: string;\n  timestamp: string;\n  status: 'sent' | 'received' | 'scheduled' | 'completed';\n  duration?: number; // for calls and meetings in minutes\n}\n\nconst sampleCommunications: Communication[] = [\n  {\n    id: 1,\n    type: 'email',\n    contact: '<PERSON>',\n    subject: 'Follow-up on proposal',\n    content: 'Hi <PERSON>, I wanted to follow up on the proposal we discussed...',\n    timestamp: '2024-01-15T10:30:00',\n    status: 'sent'\n  },\n  {\n    id: 2,\n    type: 'call',\n    contact: '<PERSON>',\n    subject: 'Discovery call',\n    content: 'Discussed requirements and budget for new project',\n    timestamp: '2024-01-15T14:00:00',\n    status: 'completed',\n    duration: 45\n  },\n  {\n    id: 3,\n    type: 'text',\n    contact: '<PERSON>',\n    subject: 'Meeting reminder',\n    content: 'Hi <PERSON>, just a reminder about our meeting tomorrow at 2 PM',\n    timestamp: '2024-01-14T16:45:00',\n    status: 'sent'\n  },\n  {\n    id: 4,\n    type: 'meeting',\n    contact: '<PERSON> <PERSON>',\n    subject: 'Product demo',\n    content: 'Scheduled product demonstration and Q&A session',\n    timestamp: '2024-01-16T11:00:00',\n    status: 'scheduled',\n    duration: 60\n  },\n];\n\nconst typeIcons = {\n  email: EnvelopeIcon,\n  call: PhoneIcon,\n  text: ChatBubbleLeftIcon,\n  meeting: VideoCameraIcon,\n};\n\nconst typeColors = {\n  email: 'bg-blue-100 text-blue-800',\n  call: 'bg-green-100 text-green-800',\n  text: 'bg-purple-100 text-purple-800',\n  meeting: 'bg-orange-100 text-orange-800',\n};\n\nconst statusColors = {\n  sent: 'bg-blue-100 text-blue-800',\n  received: 'bg-green-100 text-green-800',\n  scheduled: 'bg-yellow-100 text-yellow-800',\n  completed: 'bg-gray-100 text-gray-800',\n};\n\nexport default function CommunicationsPage() {\n  const [communications, setCommunications] = useState<Communication[]>(sampleCommunications);\n  const [selectedType, setSelectedType] = useState<string>('all');\n  const [selectedStatus, setSelectedStatus] = useState<string>('all');\n  const [showComposeModal, setShowComposeModal] = useState(false);\n\n  const filteredCommunications = communications.filter(comm => {\n    const matchesType = selectedType === 'all' || comm.type === selectedType;\n    const matchesStatus = selectedStatus === 'all' || comm.status === selectedStatus;\n    return matchesType && matchesStatus;\n  });\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const getTypeStats = () => {\n    const stats = {\n      email: communications.filter(c => c.type === 'email').length,\n      call: communications.filter(c => c.type === 'call').length,\n      text: communications.filter(c => c.type === 'text').length,\n      meeting: communications.filter(c => c.type === 'meeting').length,\n    };\n    return stats;\n  };\n\n  const stats = getTypeStats();\n\n  return (\n    <div className=\"p-4 lg:p-6 space-y-4 lg:space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0\">\n        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900\">Communications</h1>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => setShowComposeModal(true)}\n            className=\"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n          >\n            <PlusIcon className=\"w-5 h-5 mr-2\" />\n            New Communication\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <EnvelopeIcon className=\"w-8 h-8 text-blue-500\" />\n            <div className=\"ml-3\">\n              <div className=\"text-2xl font-bold text-gray-900\">{stats.email}</div>\n              <div className=\"text-sm text-gray-500\">Emails</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <PhoneIcon className=\"w-8 h-8 text-green-500\" />\n            <div className=\"ml-3\">\n              <div className=\"text-2xl font-bold text-gray-900\">{stats.call}</div>\n              <div className=\"text-sm text-gray-500\">Calls</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <ChatBubbleLeftIcon className=\"w-8 h-8 text-purple-500\" />\n            <div className=\"ml-3\">\n              <div className=\"text-2xl font-bold text-gray-900\">{stats.text}</div>\n              <div className=\"text-sm text-gray-500\">Messages</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <VideoCameraIcon className=\"w-8 h-8 text-orange-500\" />\n            <div className=\"ml-3\">\n              <div className=\"text-2xl font-bold text-gray-900\">{stats.meeting}</div>\n              <div className=\"text-sm text-gray-500\">Meetings</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <select\n          value={selectedType}\n          onChange={(e) => setSelectedType(e.target.value)}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Types</option>\n          <option value=\"email\">Email</option>\n          <option value=\"call\">Call</option>\n          <option value=\"text\">Text</option>\n          <option value=\"meeting\">Meeting</option>\n        </select>\n        <select\n          value={selectedStatus}\n          onChange={(e) => setSelectedStatus(e.target.value)}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Status</option>\n          <option value=\"sent\">Sent</option>\n          <option value=\"received\">Received</option>\n          <option value=\"scheduled\">Scheduled</option>\n          <option value=\"completed\">Completed</option>\n        </select>\n      </div>\n\n      {/* Communications List */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Recent Communications</h3>\n        </div>\n        <div className=\"divide-y divide-gray-200\">\n          {filteredCommunications.map((comm) => {\n            const TypeIcon = typeIcons[comm.type];\n            return (\n              <div key={comm.id} className=\"p-6 hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className={`p-2 rounded-lg ${typeColors[comm.type]}`}>\n                    <TypeIcon className=\"w-5 h-5\" />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <h4 className=\"text-sm font-medium text-gray-900\">{comm.subject}</h4>\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[comm.status]}`}>\n                          {comm.status.charAt(0).toUpperCase() + comm.status.slice(1)}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-500\">\n                        <ClockIcon className=\"w-4 h-4 mr-1\" />\n                        {formatTimestamp(comm.timestamp)}\n                      </div>\n                    </div>\n                    <div className=\"mt-1 flex items-center space-x-4\">\n                      <div className=\"flex items-center text-sm text-gray-500\">\n                        <UserIcon className=\"w-4 h-4 mr-1\" />\n                        {comm.contact}\n                      </div>\n                      {comm.duration && (\n                        <div className=\"text-sm text-gray-500\">\n                          Duration: {comm.duration} min\n                        </div>\n                      )}\n                    </div>\n                    <p className=\"mt-2 text-sm text-gray-600 line-clamp-2\">{comm.content}</p>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"text-blue-600 hover:text-blue-900 text-sm\">\n                      View\n                    </button>\n                    <button className=\"text-gray-600 hover:text-gray-900 text-sm\">\n                      Reply\n                    </button>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4\">\n        <button className=\"flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors\">\n          <EnvelopeIcon className=\"w-6 h-6 text-blue-600 mr-3\" />\n          <span className=\"text-blue-700 font-medium\">Send Email</span>\n        </button>\n        <button className=\"flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors\">\n          <PhoneIcon className=\"w-6 h-6 text-green-600 mr-3\" />\n          <span className=\"text-green-700 font-medium\">Make Call</span>\n        </button>\n        <button className=\"flex items-center justify-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors\">\n          <ChatBubbleLeftIcon className=\"w-6 h-6 text-purple-600 mr-3\" />\n          <span className=\"text-purple-700 font-medium\">Send Message</span>\n        </button>\n        <button className=\"flex items-center justify-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors\">\n          <VideoCameraIcon className=\"w-6 h-6 text-orange-600 mr-3\" />\n          <span className=\"text-orange-700 font-medium\">Schedule Meeting</span>\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAwBA,MAAM,uBAAwC;IAC5C;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,QAAQ;QACR,UAAU;IACZ;CACD;AAED,MAAM,YAAY;IAChB,OAAO,uNAAA,CAAA,eAAY;IACnB,MAAM,iNAAA,CAAA,YAAS;IACf,MAAM,mOAAA,CAAA,qBAAkB;IACxB,SAAS,6NAAA,CAAA,kBAAe;AAC1B;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;AACX;AAEA,MAAM,eAAe;IACnB,MAAM;IACN,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA;QACnD,MAAM,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAC5D,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,MAAM,KAAK;QAClE,OAAO,eAAe;IACxB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC5G;IAEA,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ,OAAO,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;YAC5D,MAAM,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;YAC1D,MAAM,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;YAC1D,SAAS,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;QAClE;QACA,OAAO;IACT;IAEA,MAAM,QAAQ;IAEd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,8OAAC,+MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,KAAK;;;;;;sDAC9D,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,IAAI;;;;;;sDAC7D,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mOAAA,CAAA,qBAAkB;oCAAC,WAAU;;;;;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,IAAI;;;;;;sDAC7D,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,OAAO;;;;;;sDAChE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAU;;;;;;;;;;;;kCAE1B,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,8OAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,8OAAC;gCAAO,OAAM;0CAAY;;;;;;;;;;;;;;;;;;0BAK9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;kCACZ,uBAAuB,GAAG,CAAC,CAAC;4BAC3B,MAAM,WAAW,SAAS,CAAC,KAAK,IAAI,CAAC;4BACrC,qBACE,8OAAC;gCAAkB,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC,EAAE;sDACvD,cAAA,8OAAC;gDAAS,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAqC,KAAK,OAAO;;;;;;8EAC/D,8OAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,CAAC,KAAK,MAAM,CAAC,EAAE;8EACrG,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;sEAG7D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEACpB,gBAAgB,KAAK,SAAS;;;;;;;;;;;;;8DAGnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,+MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,KAAK,OAAO;;;;;;;wDAEd,KAAK,QAAQ,kBACZ,8OAAC;4DAAI,WAAU;;gEAAwB;gEAC1B,KAAK,QAAQ;gEAAC;;;;;;;;;;;;;8DAI/B,8OAAC;oDAAE,WAAU;8DAA2C,KAAK,OAAO;;;;;;;;;;;;sDAEtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAA4C;;;;;;8DAG9D,8OAAC;oDAAO,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;+BAnC1D,KAAK,EAAE;;;;;wBA0CrB;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;gCAAK,WAAU;0CAA4B;;;;;;;;;;;;kCAE9C,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,iNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;kCAE/C,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,mOAAA,CAAA,qBAAkB;gCAAC,WAAU;;;;;;0CAC9B,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAEhD,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,6NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;0CAC3B,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;AAKxD", "debugId": null}}, {"offset": {"line": 3309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/components/AnalyticsPage.tsx"], "sourcesContent": ["'use client';\n\nimport { \n  ArrowUpIcon, \n  ArrowDownIcon,\n  CalendarIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer, \n  BarChart, \n  Bar, \n  PieChart, \n  Pie, \n  Cell,\n  AreaChart,\n  Area\n} from 'recharts';\n\n// Sample data for analytics\nconst salesTrendData = [\n  { month: 'Jan', revenue: 45000, leads: 120, conversions: 24 },\n  { month: 'Feb', revenue: 52000, leads: 135, conversions: 28 },\n  { month: 'Mar', revenue: 48000, leads: 110, conversions: 22 },\n  { month: 'Apr', revenue: 61000, leads: 150, conversions: 35 },\n  { month: 'May', revenue: 55000, leads: 140, conversions: 30 },\n  { month: 'Jun', revenue: 67000, leads: 160, conversions: 38 },\n];\n\nconst conversionFunnelData = [\n  { stage: 'Leads', count: 1000, percentage: 100 },\n  { stage: 'Qualified', count: 400, percentage: 40 },\n  { stage: 'Proposal', count: 200, percentage: 20 },\n  { stage: 'Negotiation', count: 100, percentage: 10 },\n  { stage: 'Closed Won', count: 50, percentage: 5 },\n];\n\nconst communicationEffectivenessData = [\n  { type: 'Email', sent: 450, responses: 180, rate: 40 },\n  { type: 'Phone', sent: 120, responses: 84, rate: 70 },\n  { type: 'Text', sent: 200, responses: 140, rate: 70 },\n  { type: 'Meeting', sent: 60, responses: 54, rate: 90 },\n];\n\nconst leadSourceROI = [\n  { source: 'Website', cost: 5000, revenue: 25000, roi: 400, color: '#0088FE' },\n  { source: 'Social Media', cost: 3000, revenue: 15000, roi: 400, color: '#00C49F' },\n  { source: 'Email Marketing', cost: 2000, revenue: 18000, roi: 800, color: '#FFBB28' },\n  { source: 'Referrals', cost: 1000, revenue: 12000, roi: 1100, color: '#FF8042' },\n];\n\nconst kpiData = [\n  { \n    name: 'Conversion Rate', \n    value: '5.2%', \n    change: '+0.8%', \n    changeType: 'positive',\n    description: 'Leads to customers'\n  },\n  { \n    name: 'Avg Deal Size', \n    value: '$12,500', \n    change: '+15.3%', \n    changeType: 'positive',\n    description: 'Average revenue per deal'\n  },\n  { \n    name: 'Sales Cycle', \n    value: '28 days', \n    change: '-3 days', \n    changeType: 'positive',\n    description: 'Average time to close'\n  },\n  { \n    name: 'Response Rate', \n    value: '68%', \n    change: '-2.1%', \n    changeType: 'negative',\n    description: 'Communication response rate'\n  },\n];\n\nexport default function AnalyticsPage() {\n  return (\n    <div className=\"p-4 lg:p-6 space-y-4 lg:space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0\">\n        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900\">Sales Analytics</h1>\n        <div className=\"flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3\">\n          <select className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\">\n            <option>Last 6 months</option>\n            <option>Last 3 months</option>\n            <option>Last month</option>\n            <option>This year</option>\n          </select>\n          <button className=\"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\">\n            <ChartBarIcon className=\"w-5 h-5 mr-2\" />\n            Export Report\n          </button>\n        </div>\n      </div>\n\n      {/* KPI Cards */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n        {kpiData.map((kpi) => (\n          <div key={kpi.name} className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">{kpi.name}</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{kpi.value}</p>\n                <p className=\"text-xs text-gray-400 mt-1\">{kpi.description}</p>\n              </div>\n              <div className={`flex items-center text-sm font-semibold ${\n                kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {kpi.changeType === 'positive' ? (\n                  <ArrowUpIcon className=\"w-4 h-4 mr-1\" />\n                ) : (\n                  <ArrowDownIcon className=\"w-4 h-4 mr-1\" />\n                )}\n                {kpi.change}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Revenue Trend */}\n        <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Revenue Trend</h3>\n          <ResponsiveContainer width=\"100%\" height={250}>\n            <AreaChart data={salesTrendData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"month\" />\n              <YAxis />\n              <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />\n              <Area type=\"monotone\" dataKey=\"revenue\" stroke=\"#3B82F6\" fill=\"#3B82F6\" fillOpacity={0.3} />\n            </AreaChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Lead Source ROI */}\n        <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Lead Source ROI</h3>\n          <ResponsiveContainer width=\"100%\" height={250}>\n            <BarChart data={leadSourceROI}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"source\" />\n              <YAxis />\n              <Tooltip formatter={(value) => [`${value}%`, 'ROI']} />\n              <Bar dataKey=\"roi\" fill=\"#10B981\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Conversion Funnel */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Sales Conversion Funnel</h3>\n        <div className=\"space-y-4\">\n          {conversionFunnelData.map((stage, index) => (\n            <div key={stage.stage} className=\"relative\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">{stage.stage}</span>\n                <span className=\"text-sm text-gray-500\">{stage.count} ({stage.percentage}%)</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                <div \n                  className=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                  style={{ width: `${stage.percentage}%` }}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Communication Effectiveness */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Communication Effectiveness</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={communicationEffectivenessData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"type\" />\n              <YAxis />\n              <Tooltip />\n              <Bar dataKey=\"rate\" fill=\"#8B5CF6\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Lead Conversion Over Time */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Leads vs Conversions</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <LineChart data={salesTrendData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"month\" />\n              <YAxis />\n              <Tooltip />\n              <Line type=\"monotone\" dataKey=\"leads\" stroke=\"#3B82F6\" strokeWidth={2} />\n              <Line type=\"monotone\" dataKey=\"conversions\" stroke=\"#10B981\" strokeWidth={2} />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Performance Summary Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Monthly Performance Summary</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Month</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Revenue</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Leads</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Conversions</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Conversion Rate</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {salesTrendData.map((data) => (\n                <tr key={data.month} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{data.month}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">${data.revenue.toLocaleString()}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{data.leads}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{data.conversions}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {((data.conversions / data.leads) * 100).toFixed(1)}%\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;AAyBA,4BAA4B;AAC5B,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;IAC5D;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;IAC5D;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;IAC5D;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;IAC5D;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;IAC5D;QAAE,OAAO;QAAO,SAAS;QAAO,OAAO;QAAK,aAAa;IAAG;CAC7D;AAED,MAAM,uBAAuB;IAC3B;QAAE,OAAO;QAAS,OAAO;QAAM,YAAY;IAAI;IAC/C;QAAE,OAAO;QAAa,OAAO;QAAK,YAAY;IAAG;IACjD;QAAE,OAAO;QAAY,OAAO;QAAK,YAAY;IAAG;IAChD;QAAE,OAAO;QAAe,OAAO;QAAK,YAAY;IAAG;IACnD;QAAE,OAAO;QAAc,OAAO;QAAI,YAAY;IAAE;CACjD;AAED,MAAM,iCAAiC;IACrC;QAAE,MAAM;QAAS,MAAM;QAAK,WAAW;QAAK,MAAM;IAAG;IACrD;QAAE,MAAM;QAAS,MAAM;QAAK,WAAW;QAAI,MAAM;IAAG;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAK,WAAW;QAAK,MAAM;IAAG;IACpD;QAAE,MAAM;QAAW,MAAM;QAAI,WAAW;QAAI,MAAM;IAAG;CACtD;AAED,MAAM,gBAAgB;IACpB;QAAE,QAAQ;QAAW,MAAM;QAAM,SAAS;QAAO,KAAK;QAAK,OAAO;IAAU;IAC5E;QAAE,QAAQ;QAAgB,MAAM;QAAM,SAAS;QAAO,KAAK;QAAK,OAAO;IAAU;IACjF;QAAE,QAAQ;QAAmB,MAAM;QAAM,SAAS;QAAO,KAAK;QAAK,OAAO;IAAU;IACpF;QAAE,QAAQ;QAAa,MAAM;QAAM,SAAS;QAAO,KAAK;QAAM,OAAO;IAAU;CAChF;AAED,MAAM,UAAU;IACd;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;kDAAO;;;;;;kDACR,8OAAC;kDAAO;;;;;;kDACR,8OAAC;kDAAO;;;;;;kDACR,8OAAC;kDAAO;;;;;;;;;;;;0CAEV,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;wBAAmB,WAAU;kCAC5B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC,IAAI,IAAI;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAoC,IAAI,KAAK;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAA8B,IAAI,WAAW;;;;;;;;;;;;8CAE5D,8OAAC;oCAAI,WAAW,CAAC,wCAAwC,EACvD,IAAI,UAAU,KAAK,aAAa,mBAAmB,gBACnD;;wCACC,IAAI,UAAU,KAAK,2BAClB,8OAAC,qNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,8OAAC,yNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAE1B,IAAI,MAAM;;;;;;;;;;;;;uBAfP,IAAI,IAAI;;;;;;;;;;0BAuBtB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAU;oDAAC,CAAC,CAAC,EAAE,MAAM,cAAc,IAAI;oDAAE;iDAAU;;;;;;sDACxE,8OAAC,oJAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,SAAQ;4CAAU,QAAO;4CAAU,MAAK;4CAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAM3F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCAAC,MAAM;;sDACd,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAU;oDAAC,GAAG,MAAM,CAAC,CAAC;oDAAE;iDAAM;;;;;;sDACnD,8OAAC,mJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,OAAO,sBAChC,8OAAC;gCAAsB,WAAU;;kDAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqC,MAAM,KAAK;;;;;;0DAChE,8OAAC;gDAAK,WAAU;;oDAAyB,MAAM,KAAK;oDAAC;oDAAG,MAAM,UAAU;oDAAC;;;;;;;;;;;;;kDAE3E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,MAAM,UAAU,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;;;+BARnC,MAAM,KAAK;;;;;;;;;;;;;;;;0BAiB3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCAAC,MAAM;;sDACd,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,mJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAO,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,oJAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,SAAQ;4CAAQ,QAAO;4CAAU,aAAa;;;;;;sDACpE,8OAAC,oJAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,SAAQ;4CAAc,QAAO;4CAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAC/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAC/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAC/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAC/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAGnG,8OAAC;oCAAM,WAAU;8CACd,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAG,WAAU;8DAAiE,KAAK,KAAK;;;;;;8DACzF,8OAAC;oDAAG,WAAU;;wDAAoD;wDAAE,KAAK,OAAO,CAAC,cAAc;;;;;;;8DAC/F,8OAAC;oDAAG,WAAU;8DAAqD,KAAK,KAAK;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAAqD,KAAK,WAAW;;;;;;8DACnF,8OAAC;oDAAG,WAAU;;wDACX,CAAC,AAAC,KAAK,WAAW,GAAG,KAAK,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;2CAN/C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBnC", "debugId": null}}, {"offset": {"line": 4202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/Dashboard/sales-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Sidebar from '@/components/Sidebar';\nimport MobileNavigation from '@/components/MobileNavigation';\nimport DashboardOverview from '@/components/DashboardOverview';\nimport LeadsPage from '@/components/LeadsPage';\nimport ContactsPage from '@/components/ContactsPage';\nimport CommunicationsPage from '@/components/CommunicationsPage';\nimport AnalyticsPage from '@/components/AnalyticsPage';\n\nexport default function Home() {\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return <DashboardOverview />;\n      case 'leads':\n        return <LeadsPage />;\n      case 'contacts':\n        return <ContactsPage />;\n      case 'communications':\n        return <CommunicationsPage />;\n      case 'analytics':\n        return <AnalyticsPage />;\n      default:\n        return <DashboardOverview />;\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:block\">\n        <Sidebar currentPage={currentPage} setCurrentPage={setCurrentPage} />\n      </div>\n\n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed left-0 top-0 h-full w-64 bg-white\">\n            <Sidebar\n              currentPage={currentPage}\n              setCurrentPage={setCurrentPage}\n              onNavigate={() => setSidebarOpen(false)}\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Mobile Navigation */}\n        <div className=\"lg:hidden\">\n          <MobileNavigation\n            currentPage={currentPage}\n            onMenuClick={() => setSidebarOpen(true)}\n          />\n        </div>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto\">\n          {renderPage()}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,uIAAA,CAAA,UAAiB;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,8OAAC,kIAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,8OAAC,wIAAA,CAAA,UAAkB;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,mIAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,uIAAA,CAAA,UAAiB;;;;;QAC7B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAO;oBAAC,aAAa;oBAAa,gBAAgB;;;;;;;;;;;YAIpD,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuC,SAAS,IAAM,eAAe;;;;;;kCACpF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAO;4BACN,aAAa;4BACb,gBAAgB;4BAChB,YAAY,IAAM,eAAe;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;4BACf,aAAa;4BACb,aAAa,IAAM,eAAe;;;;;;;;;;;kCAKtC,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}