'use client';

import { useState } from 'react';
import {
  PlusIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftIcon,
  VideoCameraIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import CommunicationModal from './CommunicationModal';

interface Communication {
  id: number;
  type: 'email' | 'call' | 'text' | 'meeting';
  contact: string;
  subject: string;
  content: string;
  timestamp: string;
  status: 'sent' | 'received' | 'scheduled' | 'completed';
  duration?: number; // for calls and meetings in minutes
}

const sampleCommunications: Communication[] = [
  {
    id: 1,
    type: 'email',
    contact: '<PERSON>',
    subject: 'Follow-up on proposal',
    content: '<PERSON> <PERSON>, I wanted to follow up on the proposal we discussed...',
    timestamp: '2024-01-15T10:30:00',
    status: 'sent'
  },
  {
    id: 2,
    type: 'call',
    contact: '<PERSON>',
    subject: 'Discovery call',
    content: 'Discussed requirements and budget for new project',
    timestamp: '2024-01-15T14:00:00',
    status: 'completed',
    duration: 45
  },
  {
    id: 3,
    type: 'text',
    contact: '<PERSON>',
    subject: 'Meeting reminder',
    content: 'Hi <PERSON>, just a reminder about our meeting tomorrow at 2 PM',
    timestamp: '2024-01-14T16:45:00',
    status: 'sent'
  },
  {
    id: 4,
    type: 'meeting',
    contact: 'Lisa Wilson',
    subject: 'Product demo',
    content: 'Scheduled product demonstration and Q&A session',
    timestamp: '2024-01-16T11:00:00',
    status: 'scheduled',
    duration: 60
  },
];

const typeIcons = {
  email: EnvelopeIcon,
  call: PhoneIcon,
  text: ChatBubbleLeftIcon,
  meeting: VideoCameraIcon,
};

const typeColors = {
  email: 'bg-blue-100 text-blue-800',
  call: 'bg-green-100 text-green-800',
  text: 'bg-purple-100 text-purple-800',
  meeting: 'bg-orange-100 text-orange-800',
};

const statusColors = {
  sent: 'bg-blue-100 text-blue-800',
  received: 'bg-green-100 text-green-800',
  scheduled: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-gray-100 text-gray-800',
};

export default function CommunicationsPage() {
  const [communications, setCommunications] = useState<Communication[]>(sampleCommunications);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showComposeModal, setShowComposeModal] = useState(false);
  const [communicationType, setCommunicationType] = useState<'email' | 'call' | 'text' | 'meeting'>('email');

  const filteredCommunications = communications.filter(comm => {
    const matchesType = selectedType === 'all' || comm.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || comm.status === selectedStatus;
    return matchesType && matchesStatus;
  });

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getTypeStats = () => {
    const stats = {
      email: communications.filter(c => c.type === 'email').length,
      call: communications.filter(c => c.type === 'call').length,
      text: communications.filter(c => c.type === 'text').length,
      meeting: communications.filter(c => c.type === 'meeting').length,
    };
    return stats;
  };

  const stats = getTypeStats();

  const handleNewCommunication = (type: 'email' | 'call' | 'text' | 'meeting') => {
    setCommunicationType(type);
    setShowComposeModal(true);
  };

  const handleCommunicationSubmit = (communicationData: any) => {
    setCommunications([communicationData, ...communications]);
    alert(`${communicationData.type.charAt(0).toUpperCase() + communicationData.type.slice(1)} ${communicationData.status} successfully!`);
  };

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Communications</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => handleNewCommunication('email')}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            New Communication
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <EnvelopeIcon className="w-8 h-8 text-blue-500" />
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900">{stats.email}</div>
              <div className="text-sm text-gray-500">Emails</div>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <PhoneIcon className="w-8 h-8 text-green-500" />
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900">{stats.call}</div>
              <div className="text-sm text-gray-500">Calls</div>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <ChatBubbleLeftIcon className="w-8 h-8 text-purple-500" />
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900">{stats.text}</div>
              <div className="text-sm text-gray-500">Messages</div>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <VideoCameraIcon className="w-8 h-8 text-orange-500" />
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900">{stats.meeting}</div>
              <div className="text-sm text-gray-500">Meetings</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Types</option>
          <option value="email">Email</option>
          <option value="call">Call</option>
          <option value="text">Text</option>
          <option value="meeting">Meeting</option>
        </select>
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="sent">Sent</option>
          <option value="received">Received</option>
          <option value="scheduled">Scheduled</option>
          <option value="completed">Completed</option>
        </select>
      </div>

      {/* Communications List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Communications</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredCommunications.map((comm) => {
            const TypeIcon = typeIcons[comm.type];
            return (
              <div key={comm.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${typeColors[comm.type]}`}>
                    <TypeIcon className="w-5 h-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-sm font-medium text-gray-900">{comm.subject}</h4>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[comm.status]}`}>
                          {comm.status.charAt(0).toUpperCase() + comm.status.slice(1)}
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <ClockIcon className="w-4 h-4 mr-1" />
                        {formatTimestamp(comm.timestamp)}
                      </div>
                    </div>
                    <div className="mt-1 flex items-center space-x-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <UserIcon className="w-4 h-4 mr-1" />
                        {comm.contact}
                      </div>
                      {comm.duration && (
                        <div className="text-sm text-gray-500">
                          Duration: {comm.duration} min
                        </div>
                      )}
                    </div>
                    <p className="mt-2 text-sm text-gray-600 line-clamp-2">{comm.content}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900 text-sm">
                      View
                    </button>
                    <button className="text-gray-600 hover:text-gray-900 text-sm">
                      Reply
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
        <button
          onClick={() => handleNewCommunication('email')}
          className="flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <EnvelopeIcon className="w-6 h-6 text-blue-600 mr-3" />
          <span className="text-blue-700 font-medium">Send Email</span>
        </button>
        <button
          onClick={() => handleNewCommunication('call')}
          className="flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
        >
          <PhoneIcon className="w-6 h-6 text-green-600 mr-3" />
          <span className="text-green-700 font-medium">Make Call</span>
        </button>
        <button
          onClick={() => handleNewCommunication('text')}
          className="flex items-center justify-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
        >
          <ChatBubbleLeftIcon className="w-6 h-6 text-purple-600 mr-3" />
          <span className="text-purple-700 font-medium">Send Message</span>
        </button>
        <button
          onClick={() => handleNewCommunication('meeting')}
          className="flex items-center justify-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
        >
          <VideoCameraIcon className="w-6 h-6 text-orange-600 mr-3" />
          <span className="text-orange-700 font-medium">Schedule Meeting</span>
        </button>
      </div>

      {/* Modal */}
      <CommunicationModal
        isOpen={showComposeModal}
        onClose={() => setShowComposeModal(false)}
        onSubmit={handleCommunicationSubmit}
        type={communicationType}
      />
    </div>
  );
}
