(()=>{var A;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(B)}function K(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(Y){return Object.getOwnPropertyDescriptor(B,Y).enumerable})),H.push.apply(H,J)}return H}function Q(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?K(Object(H),!0).forEach(function(J){x(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):K(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function x(B,G,H){if(G=N(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function N(B){var G=z(B,"string");return U(G)=="symbol"?G:String(G)}function z(B,G){if(U(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,HB=function B(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function Y(X){return H[J]=function(){return X}}})},D={lessThanXSeconds:"\u178F\u17B7\u1785\u1787\u17B6\u1784 {{count}} \u179C\u17B7\u1793\u17B6\u1791\u17B8",xSeconds:"{{count}} \u179C\u17B7\u1793\u17B6\u1791\u17B8",halfAMinute:"\u1780\u1793\u17D2\u179B\u17C7\u1793\u17B6\u1791\u17B8",lessThanXMinutes:"\u178F\u17B7\u1785\u1787\u17B6\u1784 {{count}} \u1793\u17B6\u1791\u17B8",xMinutes:"{{count}} \u1793\u17B6\u1791\u17B8",aboutXHours:"\u1794\u17D2\u179A\u17A0\u17C2\u179B {{count}} \u1798\u17C9\u17C4\u1784",xHours:"{{count}} \u1798\u17C9\u17C4\u1784",xDays:"{{count}} \u1790\u17D2\u1784\u17C3",aboutXWeeks:"\u1794\u17D2\u179A\u17A0\u17C2\u179B {{count}} \u179F\u1794\u17D2\u178F\u17B6\u17A0\u17CD",xWeeks:"{{count}} \u179F\u1794\u17D2\u178F\u17B6\u17A0\u17CD",aboutXMonths:"\u1794\u17D2\u179A\u17A0\u17C2\u179B {{count}} \u1781\u17C2",xMonths:"{{count}} \u1781\u17C2",aboutXYears:"\u1794\u17D2\u179A\u17A0\u17C2\u179B {{count}} \u1786\u17D2\u1793\u17B6\u17C6",xYears:"{{count}} \u1786\u17D2\u1793\u17B6\u17C6",overXYears:"\u1787\u17B6\u1784 {{count}} \u1786\u17D2\u1793\u17B6\u17C6",almostXYears:"\u1787\u17B7\u178F {{count}} \u1786\u17D2\u1793\u17B6\u17C6"},S=function B(G,H,J){var Y=D[G],X=Y;if(typeof H==="number")X=X.replace("{{count}}",H.toString());if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"\u1780\u17D2\u1793\u17BB\u1784\u179A\u1799\u17C8\u1796\u17C1\u179B "+X;else return X+"\u1798\u17BB\u1793";return X};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var M={full:"EEEE do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"dd/MM/yyyy"},V={full:"h:mm:ss a",long:"h:mm:ss a",medium:"h:mm:ss a",short:"h:mm a"},R={full:"{{date}} '\u1798\u17C9\u17C4\u1784' {{time}}",long:"{{date}} '\u1798\u17C9\u17C4\u1784' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:$({formats:M,defaultWidth:"full"}),time:$({formats:V,defaultWidth:"full"}),dateTime:$({formats:R,defaultWidth:"full"})},j={lastWeek:"'\u1790\u17D2\u1784\u17C3'eeee'\u179F\u200B\u1794\u17D2\u178F\u17B6\u200B\u17A0\u17CD\u200B\u1798\u17BB\u1793\u1798\u17C9\u17C4\u1784' p",yesterday:"'\u1798\u17D2\u179F\u17B7\u179B\u1798\u17B7\u1789\u1793\u17C5\u1798\u17C9\u17C4\u1784' p",today:"'\u1790\u17D2\u1784\u17C3\u1793\u17C1\u17C7\u1798\u17C9\u17C4\u1784' p",tomorrow:"'\u1790\u17D2\u1784\u17C3\u179F\u17D2\u17A2\u17C2\u1780\u1798\u17C9\u17C4\u1784' p",nextWeek:"'\u1790\u17D2\u1784\u17C3'eeee'\u179F\u200B\u1794\u17D2\u178F\u17B6\u200B\u17A0\u17CD\u200B\u1780\u17D2\u179A\u17C4\u1799\u1798\u17C9\u17C4\u1784' p",other:"P"},w=function B(G,H,J,Y){return j[G]};function I(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(J==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):X;Y=B.formattingValues[Z]||B.formattingValues[X]}else{var C=B.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;Y=B.values[q]||B.values[C]}var T=B.argumentCallback?B.argumentCallback(G):G;return Y[T]}}var _={narrow:["\u1798.\u1782\u179F","\u1782\u179F"],abbreviated:["\u1798\u17BB\u1793\u1782.\u179F","\u1782.\u179F"],wide:["\u1798\u17BB\u1793\u1782\u17D2\u179A\u17B7\u179F\u17D2\u178F\u179F\u1780\u179A\u17B6\u1787","\u1793\u17C3\u1782\u17D2\u179A\u17B7\u179F\u17D2\u178F\u179F\u1780\u179A\u17B6\u1787"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u178F\u17D2\u179A\u17B8\u1798\u17B6\u179F\u1791\u17B8 1","\u178F\u17D2\u179A\u17B8\u1798\u17B6\u179F\u1791\u17B8 2","\u178F\u17D2\u179A\u17B8\u1798\u17B6\u179F\u1791\u17B8 3","\u178F\u17D2\u179A\u17B8\u1798\u17B6\u179F\u1791\u17B8 4"]},F={narrow:["\u1798.\u1780","\u1780.\u1798","\u1798\u17B7","\u1798.\u179F","\u17A7.\u179F","\u1798.\u1790","\u1780.\u178A","\u179F\u17B8","\u1780\u1789","\u178F\u17BB","\u179C\u17B7","\u1792"],abbreviated:["\u1798\u1780\u179A\u17B6","\u1780\u17BB\u1798\u17D2\u1797\u17C8","\u1798\u17B8\u1793\u17B6","\u1798\u17C1\u179F\u17B6","\u17A7\u179F\u1797\u17B6","\u1798\u17B7\u1790\u17BB\u1793\u17B6","\u1780\u1780\u17D2\u1780\u178A\u17B6","\u179F\u17B8\u17A0\u17B6","\u1780\u1789\u17D2\u1789\u17B6","\u178F\u17BB\u179B\u17B6","\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6","\u1792\u17D2\u1793\u17BC"],wide:["\u1798\u1780\u179A\u17B6","\u1780\u17BB\u1798\u17D2\u1797\u17C8","\u1798\u17B8\u1793\u17B6","\u1798\u17C1\u179F\u17B6","\u17A7\u179F\u1797\u17B6","\u1798\u17B7\u1790\u17BB\u1793\u17B6","\u1780\u1780\u17D2\u1780\u178A\u17B6","\u179F\u17B8\u17A0\u17B6","\u1780\u1789\u17D2\u1789\u17B6","\u178F\u17BB\u179B\u17B6","\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6","\u1792\u17D2\u1793\u17BC"]},v={narrow:["\u17A2\u17B6","\u1785","\u17A2","\u1796","\u1796\u17D2\u179A","\u179F\u17BB","\u179F"],short:["\u17A2\u17B6","\u1785","\u17A2","\u1796","\u1796\u17D2\u179A","\u179F\u17BB","\u179F"],abbreviated:["\u17A2\u17B6","\u1785","\u17A2","\u1796","\u1796\u17D2\u179A","\u179F\u17BB","\u179F"],wide:["\u17A2\u17B6\u1791\u17B7\u178F\u17D2\u1799","\u1785\u1793\u17D2\u1791","\u17A2\u1784\u17D2\u1782\u17B6\u179A","\u1796\u17BB\u1792","\u1796\u17D2\u179A\u17A0\u179F\u17D2\u1794\u178F\u17B7\u17CD","\u179F\u17BB\u1780\u17D2\u179A","\u179F\u17C5\u179A\u17CD"]},P={narrow:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"},abbreviated:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"},wide:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"}},k={narrow:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"},abbreviated:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"},wide:{am:"\u1796\u17D2\u179A\u17B9\u1780",pm:"\u179B\u17D2\u1784\u17B6\u1785",midnight:"\u200B\u1796\u17C1\u179B\u1780\u178E\u17D2\u178A\u17B6\u179B\u17A2\u1792\u17D2\u179A\u17B6\u178F\u17D2\u179A",noon:"\u1796\u17C1\u179B\u1790\u17D2\u1784\u17C3\u178F\u17D2\u179A\u1784\u17CB",morning:"\u1796\u17C1\u179B\u1796\u17D2\u179A\u17B9\u1780",afternoon:"\u1796\u17C1\u179B\u179A\u179F\u17C0\u179B",evening:"\u1796\u17C1\u179B\u179B\u17D2\u1784\u17B6\u1785",night:"\u1796\u17C1\u179B\u1799\u1794\u17CB"}},b=function B(G,H){var J=Number(G);return J.toString()},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,Y=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],X=G.match(Y);if(!X)return null;var Z=X[0],C=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(C)?c(C,function(E){return E.test(Z)}):m(C,function(E){return E.test(Z)}),T;T=B.valueCallback?B.valueCallback(q):q,T=H.valueCallback?H.valueCallback(T):T;var GB=G.slice(Z.length);return{value:T,rest:GB}}}function m(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function c(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function y(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var Y=J[0],X=G.match(B.parsePattern);if(!X)return null;var Z=B.valueCallback?B.valueCallback(X[0]):X[0];Z=H.valueCallback?H.valueCallback(Z):Z;var C=G.slice(Y.length);return{value:Z,rest:C}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(ម\.)?គស/i,abbreviated:/^(មុន)?គ\.ស/i,wide:/^(មុន|នៃ)គ្រិស្តសករាជ/i},u={any:[/^(ម|មុន)គ\.?ស/i,/^(នៃ)?គ\.?ស/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^(ត្រីមាស)(ទី)?\s?[1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(ម\.ក|ក\.ម|មិ|ម\.ស|ឧ\.ស|ម\.ថ|ក\.ដ|សី|កញ|តុ|វិ|ធ)/i,abbreviated:/^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i,wide:/^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i},s={narrow:[/^ម\.ក/i,/^ក\.ម/i,/^មិ/i,/^ម\.ស/i,/^ឧ\.ស/i,/^ម\.ថ/i,/^ក\.ដ/i,/^សី/i,/^កញ/i,/^តុ/i,/^វិ/i,/^ធ/i],any:[/^មក/i,/^កុ/i,/^មីន/i,/^មេ/i,/^ឧស/i,/^មិថ/i,/^កក/i,/^សី/i,/^កញ/i,/^តុ/i,/^វិច/i,/^ធ/i]},o={narrow:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,short:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,abbreviated:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,wide:/^(អាទិត្យ|ចន្ទ|អង្គារ|ពុធ|ព្រហស្បតិ៍|សុក្រ|សៅរ៍)/i},r={narrow:[/^អា/i,/^ច/i,/^អ/i,/^ព/i,/^ព្រ/i,/^សុ/i,/^ស/i],any:[/^អា/i,/^ច/i,/^អ/i,/^ព/i,/^ព្រ/i,/^សុ/i,/^សៅ/i]},e={narrow:/^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i,any:/^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i},a={any:{am:/^ព្រឹក/i,pm:/^ល្ងាច/i,midnight:/^ពេលកណ្ដាលអធ្រាត្រ/i,noon:/^ពេលថ្ងៃត្រង់/i,morning:/ពេលព្រឹក/i,afternoon:/ពេលរសៀល/i,evening:/ពេលល្ងាច/i,night:/ពេលយប់/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function B(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},BB={code:"km",formatDistance:S,formatLong:L,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{km:BB})})})();

//# debugId=043377BED63941C764756E2164756E21
