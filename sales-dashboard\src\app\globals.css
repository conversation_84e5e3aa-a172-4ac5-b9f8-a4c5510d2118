@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Ensure text is readable on mobile */
  .text-xs { font-size: 0.75rem; }
  .text-sm { font-size: 0.875rem; }

  /* Better touch targets */
  button, a {
    min-height: 44px;
  }

  /* Responsive tables */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Chart containers */
  .recharts-responsive-container {
    min-height: 200px !important;
  }
}

/* Improve chart readability on small screens */
@media (max-width: 640px) {
  .recharts-cartesian-axis-tick-value {
    font-size: 10px !important;
  }

  .recharts-legend-item-text {
    font-size: 12px !important;
  }
}
